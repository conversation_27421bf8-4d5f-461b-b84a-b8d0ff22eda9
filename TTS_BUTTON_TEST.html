<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tabler-icons/3.34.0/tabler-icons.min.css">
    <style>
        body {
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }
        .action-button {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 6px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .action-button:hover {
            background: #e9ecef;
            color: #495057;
        }
        .tts-button-group {
            display: flex;
            gap: 4px;
        }
        .stop-button.hidden {
            display: none;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎵 TTS Button Test</h1>
        <p>This page tests the exact TTS button functionality from the AI chat integration.</p>
        
        <!-- Test Message with TTS buttons -->
        <div class="message assistant">
            <p>Hello! This is a test message for the Text-to-Speech functionality. Click the play button to test TTS playback. This message should be read aloud when you click the play button.</p>
            <div class="message-actions">
                <button class="action-button copy-message-btn" title="Copy message">
                    <i class="ti ti-copy"></i>
                </button>
                <div class="tts-button-group">
                    <button class="action-button" title="Listen">
                        <i class="ti ti-player-play"></i>
                    </button>
                    <button class="action-button stop-button hidden" title="Stop">
                        <i class="ti ti-player-stop"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Another test message -->
        <div class="message assistant">
            <p>This is a second test message with different content. It should also work with the TTS system. Try clicking the play button on this message as well.</p>
            <div class="message-actions">
                <button class="action-button copy-message-btn" title="Copy message">
                    <i class="ti ti-copy"></i>
                </button>
                <div class="tts-button-group">
                    <button class="action-button" title="Listen">
                        <i class="ti ti-player-play"></i>
                    </button>
                    <button class="action-button stop-button hidden" title="Stop">
                        <i class="ti ti-player-stop"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Debug Log -->
        <div class="mt-4">
            <h3>Debug Log</h3>
            <div id="debugLog" class="log">TTS Button Test initialized...\n</div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Load edgeTTS -->
    <script src="/src/js/create-new/edgeTTS.js"></script>
    
    <!-- Load TTS Integration (simplified version) -->
    <script>
        // Debug logging
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        // TTS state
        let currentTTS = {
            messageDiv: null,
            listenBtn: null,
            stopBtn: null,
            isPlaying: false,
            isPaused: false
        };
        
        // Helper: Clean text for TTS
        function cleanTextForTTS(text) {
            text = text.replace(/```[\s\S]*?```/g, '');
            text = text.replace(/`[^`]*`/g, '');
            text = text.replace(/\*\*([^*]*)\*\*/g, '$1');
            text = text.replace(/\*([^*]*)\*/g, '$1');
            text = text.replace(/__([^_]*)__/g, '$1');
            text = text.replace(/_([^_]*)_/g, '$1');
            text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');
            text = text.replace(/!\[([^\]]*)\]\([^)]+\)/g, '');
            text = text.replace(/^-{3,}|_{3,}|\*{3,}$/gm, '');
            text = text.replace(/^\s*>\s+/gm, '');
            text = text.replace(/\n{3,}/g, '\n\n');
            text = text.replace(/\s+/g, ' ').trim();
            return text;
        }
        
        // Event delegation for TTS buttons
        document.addEventListener('click', async (e) => {
            // TTS play button
            if (e.target.closest('.tts-button-group .action-button:not(.stop-button)')) {
                const listenBtn = e.target.closest('.action-button');
                const messageDiv = listenBtn.closest('.message');
                const stopBtn = messageDiv.querySelector('.stop-button');
                const p = messageDiv.querySelector('p');
                if (!p) return;
                const text = p.innerText || p.textContent || '';

                log('🎵 TTS button clicked');
                log(`Text: "${text.substring(0, 50)}..."`);

                // If another message is playing, stop it
                if (currentTTS.isPlaying && currentTTS.listenBtn && currentTTS.listenBtn !== listenBtn) {
                    log('🛑 Stopping previous TTS');
                    if (window.ttsStop) window.ttsStop();
                    if (currentTTS.listenBtn) {
                        currentTTS.listenBtn.querySelector('i').className = 'ti ti-player-play';
                        currentTTS.listenBtn.title = 'Listen';
                    }
                    if (currentTTS.stopBtn) currentTTS.stopBtn.classList.add('hidden');
                }

                // Start playback
                if (!currentTTS.isPlaying || currentTTS.listenBtn !== listenBtn) {
                    log('▶️ Starting TTS playback');
                    currentTTS = { messageDiv, listenBtn, stopBtn, isPlaying: true, isPaused: false };
                    listenBtn.querySelector('i').className = 'ti ti-player-pause';
                    listenBtn.title = 'Pause';
                    if (stopBtn) stopBtn.classList.remove('hidden');

                    try {
                        const cleanText = cleanTextForTTS(text);
                        log(`🧹 Cleaned text: "${cleanText.substring(0, 100)}..."`);

                        const voice = 'en-CA-LiamNeural';
                        const rate = '1.0';

                        // Wait for TTS to be available
                        let attempts = 0;
                        while (attempts < 10 && !window.edgeTTSHandler && !window.ttsSpeak) {
                            log('⏳ Waiting for TTS handler...');
                            await new Promise(resolve => setTimeout(resolve, 100));
                            attempts++;
                        }

                        let ttsSuccess = false;
                        
                        // Try ttsSpeak function first
                        if (window.ttsSpeak && typeof window.ttsSpeak === 'function') {
                            try {
                                log('🎤 Using ttsSpeak function');
                                await window.ttsSpeak(cleanText, voice, rate);
                                ttsSuccess = true;
                                log('✅ TTS completed via ttsSpeak');
                            } catch (error) {
                                log(`⚠️ ttsSpeak failed: ${error.message}`);
                            }
                        }
                        
                        // Try edgeTTSHandler as fallback
                        if (!ttsSuccess && window.edgeTTSHandler && typeof window.edgeTTSHandler.speak === 'function') {
                            try {
                                log('🎤 Using edgeTTSHandler.speak');
                                await window.edgeTTSHandler.speak(cleanText, voice, rate);
                                ttsSuccess = true;
                                log('✅ TTS completed via edgeTTSHandler');
                            } catch (error) {
                                log(`⚠️ edgeTTSHandler failed: ${error.message}`);
                            }
                        }
                        
                        // Try browser Speech Synthesis as last resort
                        if (!ttsSuccess && 'speechSynthesis' in window) {
                            try {
                                log('🎤 Using browser Speech Synthesis API');
                                await new Promise((resolve, reject) => {
                                    const utterance = new SpeechSynthesisUtterance(cleanText);
                                    utterance.rate = parseFloat(rate) || 1.0;
                                    utterance.onend = resolve;
                                    utterance.onerror = reject;
                                    speechSynthesis.speak(utterance);
                                });
                                ttsSuccess = true;
                                log('✅ TTS completed via Speech Synthesis API');
                            } catch (error) {
                                log(`⚠️ Speech Synthesis failed: ${error.message}`);
                            }
                        }
                        
                        if (!ttsSuccess) {
                            throw new Error('All TTS methods failed');
                        }

                    } catch (error) {
                        log(`❌ TTS playback failed: ${error.message}`);
                        // Reset UI on error
                        if (currentTTS.listenBtn) {
                            currentTTS.listenBtn.querySelector('i').className = 'ti ti-player-play';
                            currentTTS.listenBtn.title = 'Listen';
                        }
                        if (currentTTS.stopBtn) currentTTS.stopBtn.classList.add('hidden');
                        currentTTS.isPlaying = false;
                        currentTTS.isPaused = false;
                    }
                } else if (!currentTTS.isPaused) {
                    // Pause
                    log('⏸️ Pausing TTS');
                    currentTTS.isPaused = true;
                    listenBtn.querySelector('i').className = 'ti ti-player-play';
                    listenBtn.title = 'Resume';
                    if (window.ttsPause) window.ttsPause();
                } else {
                    // Resume
                    log('▶️ Resuming TTS');
                    currentTTS.isPaused = false;
                    listenBtn.querySelector('i').className = 'ti ti-player-pause';
                    listenBtn.title = 'Pause';
                    if (window.ttsResume) window.ttsResume();
                }
            }
            // TTS stop button
            else if (e.target.closest('.tts-button-group .stop-button')) {
                log('🛑 Stop TTS button clicked');
                if (currentTTS.isPlaying) {
                    if (window.ttsStop) window.ttsStop();
                    if (window.edgeTTSHandler && window.edgeTTSHandler.stop) window.edgeTTSHandler.stop();
                    
                    // Reset UI
                    if (currentTTS.listenBtn) {
                        currentTTS.listenBtn.querySelector('i').className = 'ti ti-player-play';
                        currentTTS.listenBtn.title = 'Listen';
                    }
                    if (currentTTS.stopBtn) currentTTS.stopBtn.classList.add('hidden');
                    currentTTS.isPlaying = false;
                    currentTTS.isPaused = false;
                    
                    log('✅ TTS stopped');
                }
            }
        });
        
        // Listen for TTS complete event
        window.addEventListener('tts-complete', function(event) {
            log('🎵 TTS complete event received');
            if (currentTTS.listenBtn) {
                currentTTS.listenBtn.querySelector('i').className = 'ti ti-player-play';
                currentTTS.listenBtn.title = 'Listen';
            }
            if (currentTTS.stopBtn) currentTTS.stopBtn.classList.add('hidden');
            currentTTS.isPlaying = false;
            currentTTS.isPaused = false;
            log('✅ TTS UI reset completed');
        });
        
        // Check TTS availability on load
        setTimeout(() => {
            log('Checking TTS handlers...');
            log(`edgeTTSHandler: ${!!window.edgeTTSHandler} (${typeof window.edgeTTSHandler})`);
            log(`ttsSpeak: ${!!window.ttsSpeak} (${typeof window.ttsSpeak})`);
            log(`ttsPause: ${!!window.ttsPause} (${typeof window.ttsPause})`);
            log(`ttsResume: ${!!window.ttsResume} (${typeof window.ttsResume})`);
            log(`ttsStop: ${!!window.ttsStop} (${typeof window.ttsStop})`);
            log(`speechSynthesis: ${'speechSynthesis' in window}`);
            
            if (window.edgeTTSHandler) {
                log(`edgeTTSHandler methods: ${Object.getOwnPropertyNames(window.edgeTTSHandler).join(', ')}`);
            }
        }, 1000);
        
        log('TTS Button Test loaded');
    </script>
</body>
</html>
