# 🚀 Meenoe AI Proxy Setup Guide

This guide will help you set up the AI proxy server to handle API requests and bypass CORS issues.

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

## 🔧 Installation Steps

### 1. Install Dependencies

Run this command in your project root:

```bash
npm install
```

This will install:
- `express` - Web server framework
- `cors` - CORS handling middleware
- `node-fetch` - HTTP client for Node.js
- `concurrently` - Run multiple commands simultaneously

### 2. Start the Development Environment

Instead of just running `npm run dev`, now use:

```bash
npm run dev
```

This will start both:
- 🚀 **Vite dev server** on `http://localhost:5173`
- 📡 **AI proxy server** on `http://localhost:3001`

### 3. Configure Your AI Settings

1. Open your Meenoe application
2. Click the AI assistant button (bottom-right)
3. Click the settings button (⚙️) in the chat header
4. Configure your AI provider:

#### For OpenAI Compatible APIs:
- **Provider**: Select "OpenAI Compatible (Custom)"
- **Base URL**: `https://api.synthetic.new/v1`
- **API Key**: Your API key (e.g., `glhf_...`)
- **Model**: Your model name (e.g., `hf:meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8`)

#### For Standard Providers:
- **Provider**: Select your provider (OpenAI, Claude, Gemini, Ollama)
- **API Key**: Your provider's API key
- **Model**: Optional custom model

### 4. Test the Connection

Click "Test Connection" in the settings modal to verify your configuration works.

## 🔍 How It Works

### Development Mode (localhost)
- Frontend makes requests to `/api/ai/*`
- Vite proxy forwards requests to `http://localhost:3001`
- Express server handles API calls to external AI providers
- No CORS issues because requests go through your own server

### Production Mode
- You'll need to deploy the proxy server
- Update the frontend to use your deployed proxy URL
- Or configure your production server to handle AI API proxying

## 🛠️ Troubleshooting

### Server Won't Start
```bash
# Check if ports are available
netstat -an | grep 3001
netstat -an | grep 5173

# Kill processes if needed
npx kill-port 3001
npx kill-port 5173
```

### Dependencies Issues
```bash
# Clear npm cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### Proxy Not Working
1. Check console for proxy errors
2. Verify both servers are running
3. Check network tab in browser dev tools
4. Ensure requests go to `/api/ai/*` endpoints

## 📡 API Endpoints

The proxy server provides these endpoints:

- `GET /health` - Health check
- `POST /api/ai/configure` - Configure AI provider
- `POST /api/ai/chat/completions` - Chat completions
- `POST /api/ai/functions` - Function calling
- `POST /api/ai/test` - Test connection

## 🔐 Security Notes

### Development
- API keys are stored in memory on the proxy server
- Session-based configuration (not persistent)
- Only works on localhost

### Production
- Implement proper API key encryption
- Use environment variables for sensitive data
- Add authentication and rate limiting
- Use HTTPS for all communications

## 🎯 Next Steps

1. **Test your configuration** - Send a message in the AI chat
2. **Monitor the console** - Check for successful proxy requests
3. **Verify API calls** - Should see requests to your custom endpoint
4. **Deploy for production** - Set up the proxy server on your hosting platform

## 📞 Support

If you encounter issues:

1. Check the browser console for errors
2. Check the terminal for server logs
3. Run the debug commands:
   ```javascript
   window.AIChatIntegration.debugConfig();
   window.AIChatIntegration.reloadConfig();
   ```

The proxy server should resolve all CORS issues and allow you to use any OpenAI-compatible API! 🎉
