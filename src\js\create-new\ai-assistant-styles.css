/**
 * AI Assistant Styles
 * Comprehensive styling for the agentic AI assistant
 */

/* Configuration Modal */
.ai-config-modal {
    position: fixed;
    top: 0;
    left: 0;.ai-message-user .ai-message-content
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.ai-config-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.ai-config-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-config-header h5 {
    margin: 0;
    font-weight: 600;
    color: #2c3e50;
}

.ai-config-body {
    padding: 24px;
}

.ai-config-footer {
    padding: 16px 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* Enhanced AI Panel */
.ai-assistant-panel {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 380px;
    max-height: 600px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    display: none;
    flex-direction: column;
    z-index: 1000;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-assistant-panel.show {
    transform: translateY(0);
    opacity: 1;
}

.ai-assistant-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
}

.ai-assistant-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 16px;
}

.ai-assistant-header .btn-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: background 0.2s;
}

.ai-assistant-header .btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.ai-assistant-body {
    display: flex;
    flex-direction: column;
    height: 500px;
}

.ai-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    scroll-behavior: smooth;
}

.ai-message {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ai-message-user {
    flex-direction: row-reverse;
}

.ai-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
}

.ai-message-user .ai-avatar {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.ai-message-bot .ai-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.ai-message-system .ai-avatar {
    background: #ffc107;
    color: white;
    border-color: transparent;
}

.ai-message-content {
    flex: 1;
    min-width: 0;
}

.ai-message-content p {
    margin: 0;
    padding: 12px 16px;
    border-radius: 12px;
    line-height: 1.5;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.ai-message-user .ai-message-content p {
    color: white;
    border-bottom-right-radius: 4px;
}

.ai-message-bot .ai-message-content p {
    background: #f8f9fa;
    color: #2c3e50;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 4px;
}

.ai-message-system .ai-message-content p {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
}

.ai-input-container {
    padding: 16px;
    border-top: 1px solid #f1f3f4;
    display: flex;
    gap: 8px;
    background: #fafbfc;
    border-radius: 0 0 16px 16px;
}

.ai-input-container input {
    flex: 1;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 10px 16px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.ai-input-container input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ai-input-container button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.ai-input-container button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.ai-input-container button:active {
    transform: scale(0.95);
}

/* Typing Indicator */
.ai-typing-indicator {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    border-bottom-left-radius: 4px;
}

.ai-typing-dots {
    display: flex;
    gap: 4px;
}

.ai-typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #6c757d;
    animation: typingDot 1.4s infinite ease-in-out;
}

.ai-typing-dot:nth-child(1) { animation-delay: -0.32s; }
.ai-typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Configuration Prompt */
.ai-config-prompt {
    text-align: center;
    padding: 16px;
}

.ai-config-prompt p {
    margin-bottom: 12px;
    color: #6c757d;
}

.ai-config-prompt button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: transform 0.2s;
}

.ai-config-prompt button:hover {
    transform: translateY(-1px);
}

/* Proactive Suggestions */
.ai-suggestion-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff6b6b;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-assistant-panel {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
        bottom: 20px;
        max-height: 70vh;
    }
    
    .ai-config-modal-content {
        width: 95%;
        margin: 20px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .ai-assistant-panel {
        background: #2c3e50;
        border-color: #34495e;
    }
    
    .ai-assistant-header h5 {
        color: white;
    }
    
    .ai-messages {
        background: #2c3e50;
    }
    
    .ai-message-bot .ai-message-content p {
        background: #34495e;
        color: #ecf0f1;
        border-color: #4a5f7a;
    }
    
    .ai-input-container {
        background: #34495e;
        border-color: #4a5f7a;
    }
    
    .ai-input-container input {
        background: #2c3e50;
        color: #ecf0f1;
        border-color: #4a5f7a;
    }
    
    .ai-config-modal-content {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .ai-config-header {
        border-color: #4a5f7a;
    }
}
