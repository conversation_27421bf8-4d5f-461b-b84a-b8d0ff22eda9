<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .test-section { margin-bottom: 30px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI System Test Page</h1>
        <p>This page tests the AI system components and integration.</p>
        
        <div class="test-section">
            <h3>System Status</h3>
            <div id="systemStatus" class="status warning">Checking system status...</div>
            <button class="btn btn-primary" onclick="runSystemTest()">Run System Test</button>
            <button class="btn btn-secondary" onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-section">
            <h3>AI Chat Test</h3>
            <p>Click the AI button in the bottom-right corner to test the chat interface.</p>
            <div id="chatStatus" class="status warning">Chat not tested yet</div>
            <button class="btn btn-success" onclick="testChatIntegration()">Test Chat Integration</button>
        </div>
        
        <div class="test-section">
            <h3>Component Loading Test</h3>
            <div id="componentStatus" class="status warning">Components not checked</div>
            <button class="btn btn-info" onclick="testComponents()">Test Components</button>
        </div>
        
        <div class="test-section">
            <h3>Event Delegation Test</h3>
            <div id="eventStatus" class="status warning">Events not tested</div>
            <button class="btn btn-warning" onclick="testEventDelegation()">Test Event Delegation</button>
        </div>
        
        <div class="test-section">
            <h3>System Log</h3>
            <div id="testLog" class="log">Test log will appear here...</div>
        </div>
    </div>

    <!-- AI Assistant Button (simulating the one from create-new.js) -->
    <button id="aiAssistantButton" class="ai-assistant-btn" aria-label="AI Assistant" data-bs-toggle="offcanvas" data-bs-target="#aiChatOffcanvas">
        <i class="ti ti-sparkles fs-5"></i>
        <span class="ai-notification-badge" id="aiNotificationBadge" style="display: none;">!</span>
    </button>

    <!-- AI Chat Off-canvas (simulating the one from create-new.js) -->
    <div class="offcanvas offcanvas-end ai-chat-offcanvas" tabindex="-1" id="aiChatOffcanvas" aria-labelledby="aiChatOffcanvasLabel">
        <div class="offcanvas-header ai-chat-header">
            <div class="d-flex align-items-center">
                <div class="ai-avatar-header">🤖</div>
                <div class="ms-3">
                    <h5 class="offcanvas-title mb-0" id="aiChatOffcanvasLabel">AI Assistant</h5>
                    <small class="text-muted" id="aiStatusText">Ready to help</small>
                </div>
            </div>
            <div class="d-flex align-items-center gap-2">
                <button type="button" class="btn btn-sm btn-outline-light" id="newChatBtn" title="New Chat">
                    <i class="ti ti-message-plus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-light" id="aiSettingsBtn" title="AI Settings">
                    <i class="ti ti-settings"></i>
                </button>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
        </div>
        
        <div class="offcanvas-body ai-chat-body p-0">
            <div class="ai-messages-container" id="aiMessagesContainer">
                <div class="ai-welcome-message">
                    <div class="ai-message ai-message-assistant">
                        <div class="ai-avatar">🤖</div>
                        <div class="ai-message-content">
                            <p>Hello! I'm your AI assistant. This is a test environment.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="ai-typing-indicator" id="aiTypingIndicator" style="display: none;">
                <div class="ai-avatar">🤖</div>
                <div class="ai-typing-content">
                    <div class="ai-typing-dots">
                        <span class="ai-typing-dot"></span>
                        <span class="ai-typing-dot"></span>
                        <span class="ai-typing-dot"></span>
                    </div>
                    <small class="text-muted">AI is thinking...</small>
                </div>
            </div>
            
            <div class="ai-quick-actions" id="aiQuickActions">
                <div class="quick-action-buttons">
                    <button class="btn btn-sm btn-outline-primary" data-action="test">
                        <i class="ti ti-test-pipe"></i> Test Action
                    </button>
                </div>
            </div>
        </div>
        
        <div class="ai-chat-footer">
            <div class="ai-input-container">
                <div class="ai-input-wrapper">
                    <input type="text" class="form-control ai-message-input" id="aiMessageInput" 
                           placeholder="Type your message..." autocomplete="off">
                    <button class="btn btn-primary ai-send-btn" id="aiSendBtn" title="Send message">
                        <i class="ti ti-send"></i>
                    </button>
                </div>
            </div>
            <div class="ai-footer-info">
                <small class="text-muted">
                    <span id="aiProviderInfo">AI Provider: Test Mode</span> • 
                    <span id="aiRateLimitInfo">Rate limit: OK</span>
                </small>
            </div>
        </div>
    </div>

    <!-- Include the AI system styles -->
    <style>
        /* Copy the AI styles from create-new.js here for testing */
        .ai-assistant-btn {
            position: fixed !important;
            bottom: 2rem !important;
            right: 2rem !important;
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            color: white !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
            transition: all 0.3s ease !important;
            z-index: 1040 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }
        
        .ai-chat-offcanvas {
            width: 420px !important;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-left: 1px solid #404040;
        }
        
        .ai-chat-header {
            background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
            border-bottom: 1px solid #404040;
            color: white;
        }
        
        .ai-avatar-header {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .ai-chat-body {
            background: #1a1a1a;
            color: white;
        }
        
        .ai-messages-container {
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .ai-message {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .ai-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .ai-message-content p {
            background: #2d2d2d;
            color: #e0e0e0;
            padding: 0.75rem 1rem;
            border-radius: 12px;
            margin: 0;
        }
        
        .ai-chat-footer {
            background: #2d2d2d;
            border-top: 1px solid #404040;
            padding: 1rem;
        }
        
        .ai-input-wrapper {
            display: flex;
            align-items: center;
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 24px;
            padding: 0.25rem;
        }
        
        .ai-message-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #e0e0e0;
            padding: 0.6rem 0.75rem;
        }
        
        .ai-send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Load AI System Scripts -->
    <script src="/src/js/create-new/ai-system-loader.js"></script>
    
    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
            console.log(`[AI Test] ${message}`);
        }
        
        function updateLogDisplay() {
            const logElement = document.getElementById('testLog');
            logElement.innerHTML = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            testLog = [];
            updateLogDisplay();
        }
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        function runSystemTest() {
            log('Starting system test...');

            // Test 1: Check if AI system loader exists
            if (typeof window.loadAISystem === 'function') {
                log('✅ AI System Loader found');
                updateStatus('systemStatus', 'AI System Loader: OK', 'success');
            } else {
                log('❌ AI System Loader not found');
                updateStatus('systemStatus', 'AI System Loader: MISSING', 'error');
                return;
            }

            // Test 2: Check if components are loading
            setTimeout(() => {
                if (window.AIAssistant) {
                    log('✅ AI Assistant global object found');
                    updateStatus('systemStatus', 'AI System: LOADED', 'success');
                } else {
                    log('⏳ AI Assistant still loading...');
                    updateStatus('systemStatus', 'AI System: LOADING', 'warning');
                }

                // Test 3: Check error handling
                testErrorHandling();
            }, 2000);
        }

        function testErrorHandling() {
            log('Testing error handling...');

            // Test Meenoe integration fallbacks
            if (window.MeenoeAIIntegration) {
                try {
                    const integration = new window.MeenoeAIIntegration();
                    const state = integration.getCurrentMeenoeState();
                    log('✅ Meenoe integration handles missing state gracefully');
                    log(`📊 Empty state returned: ${JSON.stringify(state).substring(0, 100)}...`);
                } catch (error) {
                    log(`❌ Meenoe integration error: ${error.message}`);
                }
            }

            // Test agentic engine error handling
            if (window.AgenticEngine) {
                try {
                    log('✅ Agentic engine class available');
                } catch (error) {
                    log(`❌ Agentic engine error: ${error.message}`);
                }
            }
        }
        
        function testChatIntegration() {
            log('Testing chat integration...');
            
            // Test if off-canvas elements exist
            const offcanvas = document.getElementById('aiChatOffcanvas');
            const input = document.getElementById('aiMessageInput');
            const sendBtn = document.getElementById('aiSendBtn');
            
            if (offcanvas && input && sendBtn) {
                log('✅ Chat UI elements found');
                updateStatus('chatStatus', 'Chat UI: OK', 'success');
                
                // Test opening the chat
                const bsOffcanvas = new bootstrap.Offcanvas(offcanvas);
                bsOffcanvas.show();
                log('✅ Chat opened successfully');
                
                setTimeout(() => {
                    bsOffcanvas.hide();
                    log('✅ Chat closed successfully');
                }, 2000);
                
            } else {
                log('❌ Chat UI elements missing');
                updateStatus('chatStatus', 'Chat UI: MISSING ELEMENTS', 'error');
            }
        }
        
        function testComponents() {
            log('Testing component loading...');
            
            const components = [
                'window.AIAssistant',
                'window.AIChatIntegration',
                'window.loadAISystem'
            ];
            
            let loadedCount = 0;
            components.forEach(component => {
                const exists = eval(`typeof ${component} !== 'undefined'`);
                if (exists) {
                    log(`✅ ${component} loaded`);
                    loadedCount++;
                } else {
                    log(`❌ ${component} not loaded`);
                }
            });
            
            if (loadedCount === components.length) {
                updateStatus('componentStatus', `All ${loadedCount} components loaded`, 'success');
            } else {
                updateStatus('componentStatus', `${loadedCount}/${components.length} components loaded`, 'warning');
            }
        }
        
        function testEventDelegation() {
            log('Testing event delegation...');
            
            // Simulate clicking the send button
            const sendBtn = document.getElementById('aiSendBtn');
            if (sendBtn) {
                log('✅ Send button found');
                
                // Add test message
                const input = document.getElementById('aiMessageInput');
                if (input) {
                    input.value = 'Test message';
                    log('✅ Test message added to input');
                    
                    // Simulate click
                    sendBtn.click();
                    log('✅ Send button clicked');
                    
                    updateStatus('eventStatus', 'Event delegation: WORKING', 'success');
                } else {
                    log('❌ Input not found');
                    updateStatus('eventStatus', 'Event delegation: INPUT MISSING', 'error');
                }
            } else {
                log('❌ Send button not found');
                updateStatus('eventStatus', 'Event delegation: BUTTON MISSING', 'error');
            }
        }
        
        // Auto-run system test on load
        window.addEventListener('load', () => {
            log('Page loaded, starting auto-test...');
            setTimeout(runSystemTest, 1000);
        });
        
        // Listen for AI system events
        window.addEventListener('aiSystemReady', () => {
            log('🎉 AI System Ready event received!');
            updateStatus('systemStatus', 'AI System: READY', 'success');
        });
        
        window.addEventListener('aiSystemError', (e) => {
            log(`❌ AI System Error: ${e.detail.message}`);
            updateStatus('systemStatus', 'AI System: ERROR', 'error');
        });
    </script>
</body>
</html>
