<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS Test - Meenoe</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tabler-icons/3.34.0/tabler-icons.min.css">
    <style>
        body {
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }
        .action-button {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 6px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .action-button:hover {
            background: #e9ecef;
            color: #495057;
        }
        .tts-button-group {
            display: flex;
            gap: 4px;
        }
        .stop-button.hidden {
            display: none;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎵 TTS Test Page</h1>
        <p>This page tests the Text-to-Speech functionality using the edgeTTS library.</p>
        
        <div class="alert alert-info">
            <strong>Instructions:</strong> Click the play button next to any message to test TTS playback.
        </div>
        
        <!-- Test Messages -->
        <div class="message assistant">
            <p>Hello! This is a test message for the Text-to-Speech functionality. The TTS system should be able to read this message aloud when you click the play button.</p>
            <div class="message-actions">
                <button class="action-button copy-message-btn" title="Copy message">
                    <i class="ti ti-copy"></i>
                </button>
                <div class="tts-button-group">
                    <button class="action-button" title="Listen">
                        <i class="ti ti-player-play"></i>
                    </button>
                    <button class="action-button stop-button hidden" title="Stop">
                        <i class="ti ti-player-stop"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="message assistant">
            <p>This is another test message with some **markdown** formatting and `code snippets` that should be cleaned up before being sent to the TTS engine.</p>
            <div class="message-actions">
                <button class="action-button copy-message-btn" title="Copy message">
                    <i class="ti ti-copy"></i>
                </button>
                <div class="tts-button-group">
                    <button class="action-button" title="Listen">
                        <i class="ti ti-player-play"></i>
                    </button>
                    <button class="action-button stop-button hidden" title="Stop">
                        <i class="ti ti-player-stop"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="message assistant">
            <p>Here's a longer message to test the TTS system with more content. This message contains multiple sentences. It should demonstrate how the system handles longer text. The TTS engine should be able to process this entire message and play it back smoothly.</p>
            <div class="message-actions">
                <button class="action-button copy-message-btn" title="Copy message">
                    <i class="ti ti-copy"></i>
                </button>
                <div class="tts-button-group">
                    <button class="action-button" title="Listen">
                        <i class="ti ti-player-play"></i>
                    </button>
                    <button class="action-button stop-button hidden" title="Stop">
                        <i class="ti ti-player-stop"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Manual Test Controls -->
        <div class="mt-4">
            <h3>Manual Test Controls</h3>
            <div class="row">
                <div class="col-md-8">
                    <textarea id="testText" class="form-control" rows="3" placeholder="Enter text to test TTS...">Hello, this is a manual test of the Text-to-Speech system.</textarea>
                </div>
                <div class="col-md-4">
                    <button id="testTTS" class="btn btn-primary w-100">Test TTS</button>
                    <button id="stopTTS" class="btn btn-danger w-100 mt-2">Stop TTS</button>
                </div>
            </div>
        </div>
        
        <!-- Debug Log -->
        <div class="mt-4">
            <h3>Debug Log</h3>
            <div id="debugLog" class="log">TTS Test initialized...\n</div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Load edgeTTS -->
    <script src="/src/js/create-new/edgeTTS.js"></script>
    
    <!-- Load TTS Integration -->
    <script src="/src/js/create-new/ai-chat-integration.js"></script>
    
    <script>
        // Debug logging
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        // Test TTS availability
        setTimeout(() => {
            log('Checking TTS handlers...');
            log(`edgeTTSHandler: ${!!window.edgeTTSHandler} (${typeof window.edgeTTSHandler})`);
            log(`ttsSpeak: ${!!window.ttsSpeak} (${typeof window.ttsSpeak})`);
            log(`ttsPause: ${!!window.ttsPause} (${typeof window.ttsPause})`);
            log(`ttsResume: ${!!window.ttsResume} (${typeof window.ttsResume})`);
            log(`ttsStop: ${!!window.ttsStop} (${typeof window.ttsStop})`);
            
            if (window.edgeTTSHandler) {
                log(`edgeTTSHandler methods: ${Object.getOwnPropertyNames(window.edgeTTSHandler).join(', ')}`);
            }
        }, 1000);
        
        // Manual test controls
        document.getElementById('testTTS').addEventListener('click', async () => {
            const text = document.getElementById('testText').value;
            log(`Testing TTS with text: "${text.substring(0, 50)}..."`);

            try {
                let ttsSuccess = false;

                // Method 1: Try ttsSpeak function
                if (window.ttsSpeak && typeof window.ttsSpeak === 'function') {
                    try {
                        log('Using ttsSpeak function...');
                        await window.ttsSpeak(text);
                        log('TTS completed successfully via ttsSpeak');
                        ttsSuccess = true;
                    } catch (error) {
                        log(`ttsSpeak failed: ${error.message}`);
                    }
                }

                // Method 2: Try edgeTTSHandler.speak
                if (!ttsSuccess && window.edgeTTSHandler && typeof window.edgeTTSHandler.speak === 'function') {
                    try {
                        log('Using edgeTTSHandler.speak...');
                        await window.edgeTTSHandler.speak(text);
                        log('TTS completed successfully via edgeTTSHandler');
                        ttsSuccess = true;
                    } catch (error) {
                        log(`edgeTTSHandler.speak failed: ${error.message}`);
                    }
                }

                // Method 3: Try browser Speech Synthesis API
                if (!ttsSuccess && 'speechSynthesis' in window) {
                    try {
                        log('Using browser Speech Synthesis API...');
                        await new Promise((resolve, reject) => {
                            const utterance = new SpeechSynthesisUtterance(text);
                            utterance.rate = 1.0;
                            utterance.onend = resolve;
                            utterance.onerror = reject;
                            speechSynthesis.speak(utterance);
                        });
                        log('TTS completed successfully via Speech Synthesis API');
                        ttsSuccess = true;
                    } catch (error) {
                        log(`Speech Synthesis API failed: ${error.message}`);
                    }
                }

                if (!ttsSuccess) {
                    log('ERROR: All TTS methods failed');
                }
            } catch (error) {
                log(`ERROR: ${error.message}`);
            }
        });
        
        document.getElementById('stopTTS').addEventListener('click', () => {
            log('Stopping TTS...');
            if (window.ttsStop) {
                window.ttsStop();
                log('TTS stopped via ttsStop');
            }
            if (window.edgeTTSHandler && window.edgeTTSHandler.stop) {
                window.edgeTTSHandler.stop();
                log('TTS stopped via edgeTTSHandler.stop');
            }
        });
        
        log('TTS Test page loaded');
    </script>
</body>
</html>
