import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Store for AI configurations (in production, use a proper database)
const aiConfigurations = new Map();

// Health check endpoints
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Meenoe AI Proxy Server'
    });
});

app.get('/api/ai/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Meenoe AI Proxy Server'
    });
});

// Configuration endpoint
app.post('/api/ai/configure', (req, res) => {
    try {
        const { sessionId, provider, apiKey, baseUrl, model } = req.body;
        
        if (!sessionId || !provider || !apiKey) {
            return res.status(400).json({ 
                error: 'Missing required fields: sessionId, provider, apiKey' 
            });
        }

        // Store configuration (encrypt in production)
        aiConfigurations.set(sessionId, {
            provider,
            apiKey,
            baseUrl: baseUrl || getDefaultBaseUrl(provider),
            model: model || getDefaultModel(provider),
            timestamp: new Date().toISOString()
        });

        console.log(`🔧 AI configuration stored for session ${sessionId}:`, {
            provider,
            baseUrl: baseUrl || getDefaultBaseUrl(provider),
            model: model || getDefaultModel(provider),
            hasApiKey: !!apiKey
        });

        res.json({ 
            success: true, 
            message: 'Configuration stored successfully',
            config: {
                provider,
                baseUrl: baseUrl || getDefaultBaseUrl(provider),
                model: model || getDefaultModel(provider)
            }
        });
    } catch (error) {
        console.error('Configuration error:', error);
        res.status(500).json({ error: 'Failed to store configuration' });
    }
});

// AI Chat Completions Proxy
app.post('/api/ai/chat/completions', async (req, res) => {
    try {
        const sessionId = req.headers['x-session-id'];
        const config = aiConfigurations.get(sessionId);

        if (!config) {
            return res.status(401).json({ 
                error: 'No AI configuration found. Please configure your AI settings first.' 
            });
        }

        const { apiKey, baseUrl } = config;
        const requestBody = req.body;

        // Use model from config if not specified in request
        if (!requestBody.model && config.model) {
            requestBody.model = config.model;
        }

        console.log(`🌐 Proxying request to: ${baseUrl}/chat/completions`);
        console.log(`📝 Model: ${requestBody.model}`);

        const response = await fetch(`${baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                'User-Agent': 'Meenoe-AI-Assistant/1.0'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ API request failed: ${response.status} ${response.statusText}`);
            console.error('Error details:', errorText);
            
            return res.status(response.status).json({
                error: `AI API error: ${response.status} ${response.statusText}`,
                details: errorText
            });
        }

        // Handle streaming responses
        if (requestBody.stream) {
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
            
            response.body.pipe(res);
        } else {
            const data = await response.json();
            res.json(data);
        }

    } catch (error) {
        console.error('Proxy error:', error);
        res.status(500).json({ 
            error: 'Proxy server error', 
            message: error.message 
        });
    }
});

// AI Function Calling Proxy (for tools/functions)
app.post('/api/ai/functions', async (req, res) => {
    try {
        const sessionId = req.headers['x-session-id'];
        const config = aiConfigurations.get(sessionId);

        if (!config) {
            return res.status(401).json({ 
                error: 'No AI configuration found. Please configure your AI settings first.' 
            });
        }

        const { apiKey, baseUrl } = config;
        const requestBody = req.body;

        console.log(`🔧 Function calling request to: ${baseUrl}/chat/completions`);

        const response = await fetch(`${baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                'User-Agent': 'Meenoe-AI-Assistant/1.0'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ Function calling failed: ${response.status} ${response.statusText}`);
            
            return res.status(response.status).json({
                error: `AI API error: ${response.status} ${response.statusText}`,
                details: errorText
            });
        }

        const data = await response.json();
        res.json(data);

    } catch (error) {
        console.error('Function calling error:', error);
        res.status(500).json({ 
            error: 'Function calling error', 
            message: error.message 
        });
    }
});

// Test endpoint
app.post('/api/ai/test', async (req, res) => {
    try {
        const { provider, apiKey, baseUrl, model } = req.body;
        
        if (!provider || !apiKey) {
            return res.status(400).json({ error: 'Provider and API key required for testing' });
        }

        const testBaseUrl = baseUrl || getDefaultBaseUrl(provider);
        const testModel = model || getDefaultModel(provider);

        console.log(`🧪 Testing connection to: ${testBaseUrl}`);

        const testResponse = await fetch(`${testBaseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: testModel,
                messages: [{ role: 'user', content: 'Hello, this is a connection test.' }],
                max_tokens: 10
            })
        });

        if (testResponse.ok) {
            res.json({ 
                success: true, 
                message: 'Connection test successful',
                status: testResponse.status
            });
        } else {
            const errorText = await testResponse.text();
            res.status(testResponse.status).json({
                success: false,
                error: `Test failed: ${testResponse.status} ${testResponse.statusText}`,
                details: errorText
            });
        }

    } catch (error) {
        console.error('Test error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Test connection failed', 
            message: error.message 
        });
    }
});

// Helper functions
function getDefaultBaseUrl(provider) {
    const baseUrls = {
        'openai': 'https://api.openai.com/v1',
        'claude': 'https://api.anthropic.com/v1',
        'gemini': 'https://generativelanguage.googleapis.com/v1beta',
        'ollama': 'http://localhost:11434/v1',
        'openai-compatible': 'https://api.openai.com/v1' // Fallback
    };
    return baseUrls[provider] || baseUrls['openai'];
}

function getDefaultModel(provider) {
    const models = {
        'openai': 'gpt-4o',
        'claude': 'claude-3-5-sonnet-20241022',
        'gemini': 'gemini-pro',
        'ollama': 'llama3.1',
        'openai-compatible': 'gpt-4o' // Fallback
    };
    return models[provider] || models['openai'];
}

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Meenoe AI Proxy Server running on http://localhost:${PORT}`);
    console.log(`📡 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 Configure AI: POST http://localhost:${PORT}/api/ai/configure`);
    console.log(`💬 Chat completions: POST http://localhost:${PORT}/api/ai/chat/completions`);
});

export default app;
