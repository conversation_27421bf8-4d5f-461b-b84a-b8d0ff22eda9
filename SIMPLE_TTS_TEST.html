<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple TTS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 Simple TTS Test</h1>
        
        <textarea id="testText" placeholder="Enter text to test...">Hello, this is a test of the Text-to-Speech system. If you can hear this, the TTS is working correctly.</textarea>
        
        <div>
            <button onclick="testBrowserTTS()">Test Browser TTS</button>
            <button onclick="testEdgeTTS()">Test Edge TTS</button>
            <button onclick="testTTSSpeak()">Test ttsSpeak Function</button>
            <button onclick="stopAllTTS()">Stop All TTS</button>
        </div>
        
        <div id="log" class="log">Simple TTS Test initialized...\n</div>
    </div>

    <!-- Load edgeTTS -->
    <script src="/src/js/create-new/edgeTTS.js"></script>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // Test browser's built-in Speech Synthesis
        function testBrowserTTS() {
            const text = document.getElementById('testText').value;
            log('Testing browser Speech Synthesis API...');
            
            if ('speechSynthesis' in window) {
                try {
                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.rate = 1.0;
                    utterance.pitch = 1.0;
                    utterance.volume = 1.0;
                    
                    utterance.onstart = () => log('Browser TTS started');
                    utterance.onend = () => log('Browser TTS completed');
                    utterance.onerror = (e) => log(`Browser TTS error: ${e.error}`);
                    
                    speechSynthesis.speak(utterance);
                    log('Browser TTS utterance queued');
                } catch (error) {
                    log(`Browser TTS failed: ${error.message}`);
                }
            } else {
                log('Browser Speech Synthesis API not available');
            }
        }
        
        // Test Edge TTS directly
        async function testEdgeTTS() {
            const text = document.getElementById('testText').value;
            log('Testing Edge TTS handler...');
            
            try {
                if (window.edgeTTSHandler) {
                    log(`edgeTTSHandler available: ${typeof window.edgeTTSHandler}`);
                    log(`edgeTTSHandler methods: ${Object.getOwnPropertyNames(window.edgeTTSHandler).join(', ')}`);
                    
                    if (typeof window.edgeTTSHandler.speak === 'function') {
                        log('Calling edgeTTSHandler.speak...');
                        await window.edgeTTSHandler.speak(text, 'en-CA-LiamNeural', '1.0');
                        log('Edge TTS completed successfully');
                    } else {
                        log('edgeTTSHandler.speak is not a function');
                    }
                } else {
                    log('edgeTTSHandler not available');
                }
            } catch (error) {
                log(`Edge TTS failed: ${error.message}`);
                console.error('Edge TTS error:', error);
            }
        }
        
        // Test ttsSpeak function
        async function testTTSSpeak() {
            const text = document.getElementById('testText').value;
            log('Testing ttsSpeak function...');
            
            try {
                if (window.ttsSpeak && typeof window.ttsSpeak === 'function') {
                    log('Calling ttsSpeak...');
                    await window.ttsSpeak(text, 'en-CA-LiamNeural', '1.0');
                    log('ttsSpeak completed successfully');
                } else {
                    log('ttsSpeak function not available');
                }
            } catch (error) {
                log(`ttsSpeak failed: ${error.message}`);
                console.error('ttsSpeak error:', error);
            }
        }
        
        // Stop all TTS
        function stopAllTTS() {
            log('Stopping all TTS...');
            
            // Stop browser TTS
            if ('speechSynthesis' in window) {
                speechSynthesis.cancel();
                log('Browser TTS stopped');
            }
            
            // Stop Edge TTS
            if (window.ttsStop && typeof window.ttsStop === 'function') {
                window.ttsStop();
                log('Edge TTS stopped via ttsStop');
            }
            
            if (window.edgeTTSHandler && typeof window.edgeTTSHandler.stop === 'function') {
                window.edgeTTSHandler.stop();
                log('Edge TTS stopped via edgeTTSHandler.stop');
            }
        }
        
        // Check TTS availability on load
        setTimeout(() => {
            log('Checking TTS availability...');
            log(`Browser Speech Synthesis: ${'speechSynthesis' in window}`);
            log(`edgeTTSHandler: ${!!window.edgeTTSHandler} (${typeof window.edgeTTSHandler})`);
            log(`ttsSpeak: ${!!window.ttsSpeak} (${typeof window.ttsSpeak})`);
            log(`ttsStop: ${!!window.ttsStop} (${typeof window.ttsStop})`);
            log(`ttsPause: ${!!window.ttsPause} (${typeof window.ttsPause})`);
            log(`ttsResume: ${!!window.ttsResume} (${typeof window.ttsResume})`);
            
            if (window.edgeTTSHandler) {
                log(`edgeTTSHandler properties: ${Object.getOwnPropertyNames(window.edgeTTSHandler).join(', ')}`);
            }
        }, 1000);
        
        log('Simple TTS Test loaded');
    </script>
</body>
</html>
