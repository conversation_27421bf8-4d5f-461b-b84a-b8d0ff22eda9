# AI Assistant Integration Guide

Quick guide to integrate the comprehensive AI assistant into existing Meenoe pages.

## 🚀 Quick Start (3 Steps)

### Step 1: Add the Loader Script

Add this single line to your HTML `<head>` section:

```html
<script src="src/js/create-new/ai-system-loader.js"></script>
```

That's it! The loader will automatically:
- Load all AI components in the correct order
- Load the required CSS styles
- Initialize the complete AI system
- Set up error handling and performance monitoring

### Step 2: Ensure Meenoe Systems are Available

The AI assistant integrates with these existing Meenoe systems:

```javascript
// Required for full functionality
window.meenoeState      // State management system
window.agendaFlow       // Agenda management system  
window.tree             // Action management system
window.meenoeUsers      // User management system
window.meenoeIntegrations // Integration system
```

### Step 3: Add AI Assistant Button (Optional)

If you want a custom AI button placement, add this HTML:

```html
<button id="aiAssistantButton" class="btn btn-primary">
    🤖 AI Assistant
</button>
```

The AI system will automatically detect and enhance any button with `id="aiAssistantButton"`.

## 📋 Verification Checklist

After integration, verify these work:

- [ ] AI assistant button appears and is clickable
- [ ] Configuration modal opens when clicking "Configure AI Settings"
- [ ] Can save API key and provider settings
- [ ] Chat interface opens and accepts messages
- [ ] AI responds to basic queries (after API key setup)
- [ ] Function calls work (e.g., "create an agenda item")
- [ ] Proactive suggestions appear (if enabled)

## 🔧 Configuration

### First-Time Setup

1. Click the AI assistant button
2. Click "Configure AI Settings" 
3. Choose your AI provider:
   - **OpenAI**: Best overall, requires API key
   - **Claude**: Great for analysis, requires API key  
   - **Gemini**: Good for factual queries, requires API key
   - **Ollama**: Local/private, requires local installation
4. Enter your API key
5. Enable desired features:
   - ✅ Proactive Assistance (recommended)
   - ✅ Streaming Responses (recommended)
6. Click "Save Configuration"

### API Key Setup

#### OpenAI
1. Go to https://platform.openai.com/api-keys
2. Create a new API key
3. Copy and paste into AI assistant configuration

#### Claude (Anthropic)
1. Go to https://console.anthropic.com/
2. Generate an API key
3. Copy and paste into AI assistant configuration

#### Gemini (Google)
1. Go to https://makersuite.google.com/app/apikey
2. Create an API key
3. Copy and paste into AI assistant configuration

#### Ollama (Local)
1. Install Ollama: https://ollama.ai/
2. Run: `ollama serve`
3. No API key needed - works locally

## 🎯 Usage Examples

### Basic Commands

```
User: "Create an agenda item for budget review"
AI: ✅ Creates agenda item and confirms

User: "Add an action to review the proposal"  
AI: ✅ Creates action item with title "Review the proposal"

User: "/status"
AI: 📊 Shows meeting status with counts and details

User: "How is my meeting structured?"
AI: 📈 Provides analysis with completeness and balance scores
```

### Advanced Workflows

```
User: "Optimize my meeting"
AI: 🔄 Runs optimization workflow:
    - Analyzes current structure
    - Identifies gaps and improvements
    - Suggests specific changes
    - Can implement changes automatically

User: "Generate action items from my agenda"
AI: ⚡ Runs action generation workflow:
    - Reviews all agenda items
    - Suggests relevant action items
    - Creates them in the system
    - Assigns priorities and owners
```

## 🔍 Troubleshooting

### AI Not Responding

**Problem**: AI assistant doesn't respond to messages
**Solutions**:
1. Check API key is configured correctly
2. Verify internet connection
3. Check browser console for errors
4. Try switching to a different AI provider

### Functions Not Working

**Problem**: AI can't create agenda items or actions
**Solutions**:
1. Verify Meenoe systems are loaded:
   ```javascript
   console.log(window.agendaFlow); // Should not be undefined
   console.log(window.tree);       // Should not be undefined
   ```
2. Check browser console for integration errors
3. Refresh the page and try again

### Slow Performance

**Problem**: AI responses are very slow
**Solutions**:
1. Check network connection
2. Try a different AI provider (Gemini is often faster)
3. Disable streaming responses in configuration
4. Check provider status pages

### Configuration Issues

**Problem**: Can't save configuration or settings don't persist
**Solutions**:
1. Check browser allows localStorage
2. Clear browser cache and try again
3. Check browser console for storage errors

## 📊 Monitoring & Debugging

### Check System Status

```javascript
// Check if AI system loaded properly
console.log(window.AISystemLoader.isFullyLoaded());

// View loaded components
console.log(window.AISystemLoader.getLoadedComponents());

// Check current configuration
console.log(window.AIAssistant.getConfig());
```

### Performance Monitoring

```javascript
// View AI operation performance
console.log(window.aiPerformanceLog);

// View any AI-related errors
console.log(window.aiErrorLog);
```

### Integration Status

```javascript
// Check Meenoe integration status
const integration = window.AIAssistant.getMeenoeIntegration();
console.log(integration.getCurrentMeenoeState());
```

## 🎨 Customization

### Styling

The AI assistant uses CSS custom properties for easy theming:

```css
:root {
    --ai-primary-color: #667eea;
    --ai-secondary-color: #764ba2;
    --ai-background: white;
    --ai-text-color: #2c3e50;
}
```

### Custom Providers

Add your own AI provider:

```javascript
class MyCustomProvider extends BaseAIProvider {
    constructor() {
        super('mycustom');
        // Your implementation
    }
}

// Register after AI system loads
window.addEventListener('aiSystemReady', () => {
    const providerManager = window.AIAssistant.getProviderManager();
    providerManager.registerProvider('mycustom', new MyCustomProvider());
});
```

## 🔄 Updates

To update the AI system:

1. Replace the files in `src/js/create-new/`
2. Clear browser cache
3. Refresh the page
4. The system will automatically reinitialize

## 📞 Support

If you encounter issues:

1. Check the browser console for errors
2. Review the troubleshooting section above
3. Verify all Meenoe systems are properly loaded
4. Test with a simple command like `/status`

The AI assistant is designed to be robust and self-healing, with comprehensive error handling and fallback mechanisms.
