<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Settings Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
    <style>
        body { 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .test-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 1rem;
        }
        
        .test-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .status-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #28a745;
        }
        
        .ai-assistant-btn {
            position: fixed !important;
            bottom: 2rem !important;
            right: 2rem !important;
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            color: white !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
            transition: all 0.3s ease !important;
            z-index: 1040 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }
        
        /* Include the AI settings modal styles */
        .ai-settings-modal {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .ai-settings-modal .modal-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px 16px 0 0;
        }

        .ai-settings-modal .modal-title {
            font-weight: 600;
            color: white;
        }

        .ai-settings-modal .btn-close {
            filter: invert(1) grayscale(100%) brightness(200%);
        }

        .ai-settings-modal .modal-body {
            background: rgba(255, 255, 255, 0.95);
            color: #2c3e50;
            margin: 0;
            border-radius: 0;
        }

        .ai-settings-modal .modal-footer {
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0 0 16px 16px;
        }

        .ai-settings-modal .form-label {
            color: #2c3e50;
            font-weight: 600;
        }

        .ai-settings-modal .form-control,
        .ai-settings-modal .form-select {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.75rem;
            transition: all 0.2s ease;
        }

        .ai-settings-modal .form-control:focus,
        .ai-settings-modal .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .ai-settings-modal .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .ai-settings-modal .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .ai-settings-modal .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .ai-settings-modal .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
            border-radius: 8px;
            font-weight: 600;
        }

        .ai-settings-modal .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
            transform: translateY(-1px);
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .spin {
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🤖 AI Settings Modal Test</h1>
        
        <div class="text-center">
            <p class="lead">Test the AI settings configuration modal</p>
            
            <button class="btn test-button" onclick="testSettingsModal()">
                <i class="ti ti-settings me-2"></i>Open AI Settings Modal
            </button>
            
            <button class="btn test-button" onclick="checkSavedSettings()">
                <i class="ti ti-database me-2"></i>Check Saved Settings
            </button>
            
            <button class="btn test-button" onclick="clearSettings()">
                <i class="ti ti-trash me-2"></i>Clear Settings
            </button>
        </div>
        
        <div class="status-box">
            <h6><i class="ti ti-info-circle me-2"></i>Test Instructions:</h6>
            <ol>
                <li>Click "Open AI Settings Modal" to test the configuration interface</li>
                <li>Try different providers including "OpenAI Compatible"</li>
                <li>Test form validation and field interactions</li>
                <li>Save settings and verify they persist</li>
                <li>Use the floating AI button (bottom-right) to access settings</li>
            </ol>
        </div>
        
        <div id="testResults" class="mt-4"></div>
    </div>

    <!-- Floating AI Button -->
    <button id="aiAssistantButton" class="ai-assistant-btn" onclick="testSettingsModal()">
        <i class="ti ti-sparkles"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Tabler Icons -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons.min.js"></script>
    
    <!-- AI Chat Integration Script (for the settings modal) -->
    <script>
        // Simplified version of the settings modal functionality for testing
        
        function testSettingsModal() {
            showAISettingsModal();
        }
        
        function checkSavedSettings() {
            const settings = localStorage.getItem('meenoe_ai_config');
            const resultsDiv = document.getElementById('testResults');
            
            if (settings) {
                const config = JSON.parse(settings);
                resultsDiv.innerHTML = `
                    <div class="status-box">
                        <h6><i class="ti ti-check me-2"></i>Saved Settings Found:</h6>
                        <pre style="background: rgba(0,0,0,0.2); padding: 1rem; border-radius: 8px; color: #fff;">
${JSON.stringify(config, null, 2)}
                        </pre>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="status-box" style="border-left-color: #ffc107;">
                        <h6><i class="ti ti-alert-triangle me-2"></i>No Settings Found</h6>
                        <p>No AI configuration has been saved yet.</p>
                    </div>
                `;
            }
        }
        
        function clearSettings() {
            localStorage.removeItem('meenoe_ai_config');
            document.getElementById('testResults').innerHTML = `
                <div class="status-box" style="border-left-color: #dc3545;">
                    <h6><i class="ti ti-trash me-2"></i>Settings Cleared</h6>
                    <p>All AI configuration has been removed.</p>
                </div>
            `;
        }
        
        // Include the settings modal code from ai-chat-integration.js
        function showAISettingsModal() {
            // Remove existing modal if present
            const existingModal = document.getElementById('aiSettingsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Create modal HTML (same as in ai-chat-integration.js)
            const modal = document.createElement('div');
            modal.id = 'aiSettingsModal';
            modal.className = 'modal fade';
            modal.setAttribute('tabindex', '-1');
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content ai-settings-modal">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="ti ti-settings me-2"></i>AI Assistant Settings
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="aiSettingsForm">
                                <div class="mb-4">
                                    <label for="aiProvider" class="form-label fw-bold">
                                        <i class="ti ti-robot me-2"></i>AI Provider
                                    </label>
                                    <select class="form-select" id="aiProvider" required>
                                        <option value="">Select AI Provider</option>
                                        <option value="openai">OpenAI (GPT-4)</option>
                                        <option value="claude">Claude (Anthropic)</option>
                                        <option value="gemini">Gemini (Google)</option>
                                        <option value="ollama">Ollama (Local)</option>
                                        <option value="openai-compatible">OpenAI Compatible (Custom)</option>
                                    </select>
                                </div>

                                <div id="openaiCompatibleSettings" style="display: none;">
                                    <div class="mb-3">
                                        <label for="aiBaseUrl" class="form-label fw-bold">
                                            <i class="ti ti-link me-2"></i>Base URL
                                        </label>
                                        <input type="url" class="form-control" id="aiBaseUrl" 
                                               placeholder="https://api.example.com/v1">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="aiApiKey" class="form-label fw-bold">
                                        <i class="ti ti-key me-2"></i>API Key
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="aiApiKey" 
                                               placeholder="Enter your API key" required>
                                        <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                            <i class="ti ti-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="aiModel" class="form-label fw-bold">
                                        <i class="ti ti-cpu me-2"></i>Model
                                    </label>
                                    <input type="text" class="form-control" id="aiModel" 
                                           placeholder="Leave blank for default">
                                </div>

                                <div class="mb-4">
                                    <h6 class="fw-bold mb-3">Advanced Settings</h6>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableProactive" checked>
                                        <label class="form-check-label" for="enableProactive">
                                            Enable Proactive Assistance
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableStreaming" checked>
                                        <label class="form-check-label" for="enableStreaming">
                                            Enable Streaming Responses
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="saveTestSettings()">
                                Save Configuration
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            setupTestEventListeners();
            loadCurrentSettings();
            
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
            
            modal.addEventListener('hidden.bs.modal', () => modal.remove());
        }
        
        function setupTestEventListeners() {
            // Provider change handler
            document.getElementById('aiProvider').addEventListener('change', (e) => {
                const isOpenAICompatible = e.target.value === 'openai-compatible';
                document.getElementById('openaiCompatibleSettings').style.display = 
                    isOpenAICompatible ? 'block' : 'none';
                
                const apiKeyInput = document.getElementById('aiApiKey');
                const placeholders = {
                    'openai': 'sk-...',
                    'claude': 'sk-ant-...',
                    'gemini': 'AI...',
                    'ollama': 'Not required for local Ollama',
                    'openai-compatible': 'Your custom API key'
                };
                apiKeyInput.placeholder = placeholders[e.target.value] || 'Enter your API key';
                apiKeyInput.disabled = e.target.value === 'ollama';
                apiKeyInput.required = e.target.value !== 'ollama';
            });

            // Toggle API key visibility
            document.getElementById('toggleApiKey').addEventListener('click', () => {
                const apiKeyInput = document.getElementById('aiApiKey');
                const icon = document.getElementById('toggleApiKey').querySelector('i');
                
                if (apiKeyInput.type === 'password') {
                    apiKeyInput.type = 'text';
                    icon.className = 'ti ti-eye-off';
                } else {
                    apiKeyInput.type = 'password';
                    icon.className = 'ti ti-eye';
                }
            });
        }
        
        function loadCurrentSettings() {
            try {
                const savedConfig = localStorage.getItem('meenoe_ai_config');
                if (savedConfig) {
                    const config = JSON.parse(savedConfig);
                    document.getElementById('aiProvider').value = config.provider || '';
                    document.getElementById('aiApiKey').value = config.apiKey || '';
                    document.getElementById('aiModel').value = config.model || '';
                    document.getElementById('aiBaseUrl').value = config.baseUrl || '';
                    document.getElementById('enableProactive').checked = config.enableProactiveAssistance !== false;
                    document.getElementById('enableStreaming').checked = config.enableStreaming !== false;
                    
                    document.getElementById('aiProvider').dispatchEvent(new Event('change'));
                }
            } catch (error) {
                console.error('Error loading settings:', error);
            }
        }
        
        function saveTestSettings() {
            const config = {
                provider: document.getElementById('aiProvider').value,
                apiKey: document.getElementById('aiApiKey').value,
                model: document.getElementById('aiModel').value,
                baseUrl: document.getElementById('aiBaseUrl').value,
                enableProactiveAssistance: document.getElementById('enableProactive').checked,
                enableStreaming: document.getElementById('enableStreaming').checked,
                timestamp: new Date().toISOString()
            };

            if (!config.provider) {
                alert('Please select an AI provider');
                return;
            }

            if (config.provider !== 'ollama' && !config.apiKey) {
                alert('Please enter an API key');
                return;
            }

            if (config.provider === 'openai-compatible' && !config.baseUrl) {
                alert('Please enter a base URL for OpenAI compatible provider');
                return;
            }

            try {
                localStorage.setItem('meenoe_ai_config', JSON.stringify(config));

                // Store configuration globally for testing
                window.meenoeAIConfig = config;

                const modal = bootstrap.Modal.getInstance(document.getElementById('aiSettingsModal'));
                modal.hide();

                document.getElementById('testResults').innerHTML = `
                    <div class="status-box">
                        <h6><i class="ti ti-check me-2"></i>Settings Saved Successfully!</h6>
                        <p>Configuration has been saved to localStorage and is ready for use.</p>
                        <p><strong>Provider:</strong> ${config.provider}</p>
                        <p><strong>Model:</strong> ${config.model || 'Default'}</p>
                        ${config.baseUrl ? `<p><strong>Base URL:</strong> ${config.baseUrl}</p>` : ''}
                    </div>
                `;

                console.log('✅ Test settings saved:', config);

            } catch (error) {
                console.error('Error saving test settings:', error);
                alert('Error saving settings: ' + error.message);
            }
        }
    </script>
</body>
</html>
