# AI Setup and Testing Guide

Complete guide to connect to the AI system and test all agentic features.

## 🚀 Quick Setup (5 Minutes)

### Step 1: Access the AI Assistant

1. **Open your Meenoe create-new page**
2. **Look for the floating AI button** in the bottom-right corner (purple gradient with sparkle icon)
3. **Click the AI button** to open the dark-themed off-canvas chat

### Step 2: Configure Your AI Provider

1. **Click the settings button (⚙️)** in the chat header
2. **Choose your AI provider:**
   - **OpenAI (Recommended)**: Best overall performance
   - **Claude**: Excellent for analysis and safety
   - **Gemini**: Good for factual queries
   - **Ollama**: Local/private (requires installation)

3. **Enter your API key:**

#### Getting API Keys:

**OpenAI:**
- Go to https://platform.openai.com/api-keys
- Click "Create new secret key"
- Copy the key (starts with `sk-`)

**Claude:**
- Go to https://console.anthropic.com/
- Navigate to "API Keys"
- Create a new key (starts with `sk-ant-`)

**Gemini:**
- Go to https://makersuite.google.com/app/apikey
- Create API key
- Copy the key

**Ollama (Local):**
- Install from https://ollama.ai/
- Run `ollama serve` in terminal
- No API key needed

4. **Enable recommended settings:**
   - ✅ Proactive Assistance
   - ✅ Streaming Responses

5. **Click "Save Configuration"**

### Step 3: Verify Connection

You should see:
- Status changes to "Ready to help"
- Provider info shows your selected provider with ✓
- Welcome message appears in chat

## 🧪 Testing Agentic Features

### 1. Basic Conversation Test

**Try these messages:**

```
"Hello, can you help me?"
"What can you do?"
"How does this AI system work?"
```

**Expected:** Natural conversational responses explaining capabilities.

### 2. Meenoe Integration Test

**Create agenda items:**
```
"Create an agenda item for budget review"
"Add an agenda point about quarterly planning"
```

**Create action items:**
```
"Create an action to review the proposal"
"Add a task for John to prepare the presentation"
```

**Expected:** Items appear in your Meenoe interface immediately.

### 3. Analysis and Optimization

**Meeting analysis:**
```
"Analyze my meeting structure"
"How is my meeting organized?"
"What's the status of my meeting?"
```

**Optimization requests:**
```
"Optimize my meeting structure"
"Improve the balance of my meeting"
"Suggest improvements for my agenda"
```

**Expected:** Detailed analysis with scores and specific recommendations.

### 4. Advanced Workflow Testing

**Complex workflows:**
```
"Generate action items from my agenda"
"Create a comprehensive action plan"
"Optimize my existing meeting structure"
```

**Expected:** Multi-step processes with progress updates and detailed results.

### 5. Command Testing

**Direct commands:**
```
"/status"
"/analyze" 
"/agenda Budget Discussion"
"/action Review quarterly numbers"
```

**Expected:** Immediate execution with confirmation messages.

### 6. Proactive Assistance Testing

**Trigger proactive suggestions:**

1. **Create agenda items without actions** - AI should suggest adding action items
2. **Add many critical urgency items** - AI should recommend urgency balance
3. **Leave meeting sections empty** - AI should suggest content
4. **Create imbalanced structure** - AI should offer optimization

**Expected:** Notification badge appears, proactive messages in chat.

### 7. Context Awareness Testing

**Test contextual understanding:**
```
"What's missing from this meeting?"
"How can I make this more effective?"
"What should I do next?"
```

**Expected:** Responses based on current meeting state and structure.

## 🎯 Quick Action Testing

Use the quick action buttons in the chat:

1. **📊 Analyze Meeting** - Instant meeting analysis
2. **🪄 Optimize Structure** - Run optimization workflow  
3. **✅ Generate Actions** - Create action items from agenda

## 🔍 Advanced Testing Scenarios

### Scenario 1: Empty Meeting Setup
1. Start with completely empty meeting
2. Ask AI: "Help me set up this meeting"
3. Follow AI suggestions to build complete structure

### Scenario 2: Meeting Optimization
1. Create unbalanced meeting (many agenda, few actions)
2. Ask AI: "Optimize my meeting"
3. Review and apply AI recommendations

### Scenario 3: Collaborative Planning
1. Add several agenda items
2. Ask AI: "Generate a comprehensive action plan"
3. Review generated actions and assignments

### Scenario 4: Real-time Assistance
1. Work on your meeting normally
2. Watch for proactive AI suggestions
3. Interact with suggestions as they appear

## 🛠️ Troubleshooting

### AI Not Responding

**Check:**
1. API key is correctly entered
2. Internet connection is stable
3. Provider service is operational
4. Browser console for errors

**Solutions:**
- Try different AI provider
- Refresh page and reconfigure
- Check API key format

### Functions Not Working

**Check:**
1. Meenoe systems are loaded (refresh if needed)
2. No JavaScript errors in console
3. AI has proper permissions

**Solutions:**
- Refresh the page
- Try simpler commands first
- Check browser compatibility

### Slow Responses

**Solutions:**
- Switch to faster provider (Gemini often fastest)
- Disable streaming in settings
- Check network connection
- Try shorter messages

### Proactive Features Not Working

**Check:**
1. Proactive Assistance is enabled in settings
2. You're actively using the meeting interface
3. Meeting has some content to analyze

**Solutions:**
- Enable proactive assistance in AI settings
- Create some meeting content first
- Wait a few moments for analysis

## 📊 Performance Monitoring

**Check system performance:**

```javascript
// In browser console
console.log('AI System Health:', window.AIAssistant.getConfig());
console.log('Performance Logs:', window.aiPerformanceLog);
console.log('Cache Stats:', window.AIAssistant.getCacheStats());
console.log('Rate Limits:', window.AIAssistant.getRateLimitStatus());
```

## 🎉 Success Indicators

You'll know the system is working perfectly when:

- ✅ AI responds naturally to conversations
- ✅ Function calls create actual agenda/action items
- ✅ Analysis provides meaningful insights
- ✅ Proactive suggestions appear automatically
- ✅ Workflows execute multi-step processes
- ✅ Commands work instantly
- ✅ Context awareness is evident in responses
- ✅ UI updates happen in real-time

## 🚀 Advanced Usage Tips

### Power User Commands
```
"Create 5 agenda items for a quarterly review meeting"
"Generate a complete action plan with assignments and due dates"
"Analyze and optimize this meeting for a 30-minute duration"
```

### Workflow Combinations
```
"First analyze my meeting, then optimize it, then generate actions"
"Help me restructure this as a decision-making meeting"
"Convert this brainstorming session into an action-oriented meeting"
```

### Context-Aware Requests
```
"Based on my current agenda, what actions are missing?"
"How should I prioritize these items for maximum efficiency?"
"What would make this meeting more engaging for 8 participants?"
```

## 📈 Measuring Success

**Track these metrics:**
- Response time (should be < 3 seconds)
- Function success rate (should be > 95%)
- Proactive suggestion relevance
- Meeting structure improvement scores
- User satisfaction with AI assistance

## 🔄 Continuous Improvement

**Provide feedback:**
- Rate AI suggestions
- Report any issues
- Suggest new features
- Share successful use cases

The AI system learns and improves from usage patterns and feedback!

---

## 🎯 Ready to Start?

1. **Click the AI button** (bottom-right corner)
2. **Configure your API key** (settings button)
3. **Say "Hello"** to start chatting
4. **Try the quick actions** for instant results
5. **Explore the agentic features** with complex requests

**Your AI meeting assistant is ready to transform how you plan and manage meetings!** 🚀
