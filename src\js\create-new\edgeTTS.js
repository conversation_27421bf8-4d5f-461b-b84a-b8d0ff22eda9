/*! For license information please see edgeTTS.js.LICENSE.txt */
(()=>{var t={7:t=>{"use strict";var e,n="object"==typeof Reflect?Reflect:null,r=n&&"function"==typeof n.apply?n.apply:function(t,e,n){return Function.prototype.apply.call(t,e,n)};e=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var i=Number.isNaN||function(t){return t!=t};function o(){o.init.call(this)}t.exports=o,t.exports.once=function(t,e){return new Promise((function(n,r){function i(n){t.removeListener(e,o),r(n)}function o(){"function"==typeof t.removeListener&&t.removeListener("error",i),n([].slice.call(arguments))}y(t,e,o,{once:!0}),"error"!==e&&function(t,e){"function"==typeof t.on&&y(t,"error",e,{once:!0})}(t,i)}))},o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var s=10;function u(t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function f(t){return void 0===t._maxListeners?o.defaultMaxListeners:t._maxListeners}function a(t,e,n,r){var i,o,s,a;if(u(n),void 0===(o=t._events)?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,n.listener?n.listener:n),o=t._events),s=o[e]),void 0===s)s=o[e]=n,++t._eventsCount;else if("function"==typeof s?s=o[e]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),(i=f(t))>0&&s.length>i&&!s.warned){s.warned=!0;var h=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");h.name="MaxListenersExceededWarning",h.emitter=t,h.type=e,h.count=s.length,a=h,console&&console.warn&&console.warn(a)}return t}function h(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function c(t,e,n){var r={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},i=h.bind(r);return i.listener=n,r.wrapFn=i,i}function l(t,e,n){var r=t._events;if(void 0===r)return[];var i=r[e];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(t){for(var e=new Array(t.length),n=0;n<e.length;++n)e[n]=t[n].listener||t[n];return e}(i):d(i,i.length)}function p(t){var e=this._events;if(void 0!==e){var n=e[t];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function d(t,e){for(var n=new Array(e),r=0;r<e;++r)n[r]=t[r];return n}function y(t,e,n,r){if("function"==typeof t.on)r.once?t.once(e,n):t.on(e,n);else{if("function"!=typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function i(o){r.once&&t.removeEventListener(e,i),n(o)}))}}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(t){if("number"!=typeof t||t<0||i(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");s=t}}),o.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||i(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},o.prototype.getMaxListeners=function(){return f(this)},o.prototype.emit=function(t){for(var e=[],n=1;n<arguments.length;n++)e.push(arguments[n]);var i="error"===t,o=this._events;if(void 0!==o)i=i&&void 0===o.error;else if(!i)return!1;if(i){var s;if(e.length>0&&(s=e[0]),s instanceof Error)throw s;var u=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw u.context=s,u}var f=o[t];if(void 0===f)return!1;if("function"==typeof f)r(f,this,e);else{var a=f.length,h=d(f,a);for(n=0;n<a;++n)r(h[n],this,e)}return!0},o.prototype.addListener=function(t,e){return a(this,t,e,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(t,e){return a(this,t,e,!0)},o.prototype.once=function(t,e){return u(e),this.on(t,c(this,t,e)),this},o.prototype.prependOnceListener=function(t,e){return u(e),this.prependListener(t,c(this,t,e)),this},o.prototype.removeListener=function(t,e){var n,r,i,o,s;if(u(e),void 0===(r=this._events))return this;if(void 0===(n=r[t]))return this;if(n===e||n.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,n.listener||e));else if("function"!=typeof n){for(i=-1,o=n.length-1;o>=0;o--)if(n[o]===e||n[o].listener===e){s=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(n,i),1===n.length&&(r[t]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",t,s||e)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(t){var e,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[t]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[t]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)"removeListener"!==(i=o[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=n[t]))this.removeListener(t,e);else if(void 0!==e)for(r=e.length-1;r>=0;r--)this.removeListener(t,e[r]);return this},o.prototype.listeners=function(t){return l(this,t,!0)},o.prototype.rawListeners=function(t){return l(this,t,!1)},o.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):p.call(t,e)},o.prototype.listenerCount=p,o.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]}},251:(t,e)=>{e.read=function(t,e,n,r,i){var o,s,u=8*i-r-1,f=(1<<u)-1,a=f>>1,h=-7,c=n?i-1:0,l=n?-1:1,p=t[e+c];for(c+=l,o=p&(1<<-h)-1,p>>=-h,h+=u;h>0;o=256*o+t[e+c],c+=l,h-=8);for(s=o&(1<<-h)-1,o>>=-h,h+=r;h>0;s=256*s+t[e+c],c+=l,h-=8);if(0===o)o=1-a;else{if(o===f)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,r),o-=a}return(p?-1:1)*s*Math.pow(2,o-r)},e.write=function(t,e,n,r,i,o){var s,u,f,a=8*o-i-1,h=(1<<a)-1,c=h>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=r?0:o-1,d=r?1:-1,y=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,s=h):(s=Math.floor(Math.log(e)/Math.LN2),e*(f=Math.pow(2,-s))<1&&(s--,f*=2),(e+=s+c>=1?l/f:l*Math.pow(2,1-c))*f>=2&&(s++,f/=2),s+c>=h?(u=0,s=h):s+c>=1?(u=(e*f-1)*Math.pow(2,i),s+=c):(u=e*Math.pow(2,c-1)*Math.pow(2,i),s=0));i>=8;t[n+p]=255&u,p+=d,u/=256,i-=8);for(s=s<<i|u,a+=i;a>0;t[n+p]=255&s,p+=d,s/=256,a-=8);t[n+p-d]|=128*y}},287:(t,e,n)=>{"use strict";const r=n(526),i=n(251),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;e.Buffer=f,e.SlowBuffer=function(t){return+t!=t&&(t=0),f.alloc(+t)},e.INSPECT_MAX_BYTES=50;const s=2147483647;function u(t){if(t>s)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,f.prototype),e}function f(t,e,n){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return c(t)}return a(t,e,n)}function a(t,e,n){if("string"==typeof t)return function(t,e){if("string"==typeof e&&""!==e||(e="utf8"),!f.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const n=0|y(t,e);let r=u(n);const i=r.write(t,e);return i!==n&&(r=r.slice(0,i)),r}(t,e);if(ArrayBuffer.isView(t))return function(t){if(X(t,Uint8Array)){const e=new Uint8Array(t);return p(e.buffer,e.byteOffset,e.byteLength)}return l(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(X(t,ArrayBuffer)||t&&X(t.buffer,ArrayBuffer))return p(t,e,n);if("undefined"!=typeof SharedArrayBuffer&&(X(t,SharedArrayBuffer)||t&&X(t.buffer,SharedArrayBuffer)))return p(t,e,n);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');const r=t.valueOf&&t.valueOf();if(null!=r&&r!==t)return f.from(r,e,n);const i=function(t){if(f.isBuffer(t)){const e=0|d(t.length),n=u(e);return 0===n.length||t.copy(n,0,0,e),n}return void 0!==t.length?"number"!=typeof t.length||Y(t.length)?u(0):l(t):"Buffer"===t.type&&Array.isArray(t.data)?l(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return f.from(t[Symbol.toPrimitive]("string"),e,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function h(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return h(t),u(t<0?0:0|d(t))}function l(t){const e=t.length<0?0:0|d(t.length),n=u(e);for(let r=0;r<e;r+=1)n[r]=255&t[r];return n}function p(t,e,n){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw new RangeError('"length" is outside of buffer bounds');let r;return r=void 0===e&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,e):new Uint8Array(t,e,n),Object.setPrototypeOf(r,f.prototype),r}function d(t){if(t>=s)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s.toString(16)+" bytes");return 0|t}function y(t,e){if(f.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||X(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const n=t.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;let i=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return K(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return G(t).length;default:if(i)return r?-1:K(t).length;e=(""+e).toLowerCase(),i=!0}}function g(t,e,n){let r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return _(this,e,n);case"utf8":case"utf-8":return O(this,e,n);case"ascii":return U(this,e,n);case"latin1":case"binary":return S(this,e,n);case"base64":return T(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return P(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function w(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function m(t,e,n,r,i){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),Y(n=+n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof e&&(e=f.from(e,r)),f.isBuffer(e))return 0===e.length?-1:v(t,e,n,r,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):v(t,[e],n,r,i);throw new TypeError("val must be string, number or Buffer")}function v(t,e,n,r,i){let o,s=1,u=t.length,f=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;s=2,u/=2,f/=2,n/=2}function a(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){let r=-1;for(o=n;o<u;o++)if(a(t,o)===a(e,-1===r?0:o-r)){if(-1===r&&(r=o),o-r+1===f)return r*s}else-1!==r&&(o-=o-r),r=-1}else for(n+f>u&&(n=u-f),o=n;o>=0;o--){let n=!0;for(let r=0;r<f;r++)if(a(t,o+r)!==a(e,r)){n=!1;break}if(n)return o}return-1}function E(t,e,n,r){n=Number(n)||0;const i=t.length-n;r?(r=Number(r))>i&&(r=i):r=i;const o=e.length;let s;for(r>o/2&&(r=o/2),s=0;s<r;++s){const r=parseInt(e.substr(2*s,2),16);if(Y(r))return s;t[n+s]=r}return s}function b(t,e,n,r){return q(K(e,t.length-n),t,n,r)}function B(t,e,n,r){return q(function(t){const e=[];for(let n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function A(t,e,n,r){return q(G(e),t,n,r)}function L(t,e,n,r){return q(function(t,e){let n,r,i;const o=[];for(let s=0;s<t.length&&!((e-=2)<0);++s)n=t.charCodeAt(s),r=n>>8,i=n%256,o.push(i),o.push(r);return o}(e,t.length-n),t,n,r)}function T(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function O(t,e,n){n=Math.min(t.length,n);const r=[];let i=e;for(;i<n;){const e=t[i];let o=null,s=e>239?4:e>223?3:e>191?2:1;if(i+s<=n){let n,r,u,f;switch(s){case 1:e<128&&(o=e);break;case 2:n=t[i+1],128==(192&n)&&(f=(31&e)<<6|63&n,f>127&&(o=f));break;case 3:n=t[i+1],r=t[i+2],128==(192&n)&&128==(192&r)&&(f=(15&e)<<12|(63&n)<<6|63&r,f>2047&&(f<55296||f>57343)&&(o=f));break;case 4:n=t[i+1],r=t[i+2],u=t[i+3],128==(192&n)&&128==(192&r)&&128==(192&u)&&(f=(15&e)<<18|(63&n)<<12|(63&r)<<6|63&u,f>65535&&f<1114112&&(o=f))}}null===o?(o=65533,s=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=s}return function(t){const e=t.length;if(e<=I)return String.fromCharCode.apply(String,t);let n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=I));return n}(r)}e.kMaxLength=s,f.TYPED_ARRAY_SUPPORT=function(){try{const t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),f.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(f.prototype,"parent",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.buffer}}),Object.defineProperty(f.prototype,"offset",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.byteOffset}}),f.poolSize=8192,f.from=function(t,e,n){return a(t,e,n)},Object.setPrototypeOf(f.prototype,Uint8Array.prototype),Object.setPrototypeOf(f,Uint8Array),f.alloc=function(t,e,n){return function(t,e,n){return h(t),t<=0?u(t):void 0!==e?"string"==typeof n?u(t).fill(e,n):u(t).fill(e):u(t)}(t,e,n)},f.allocUnsafe=function(t){return c(t)},f.allocUnsafeSlow=function(t){return c(t)},f.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==f.prototype},f.compare=function(t,e){if(X(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),X(e,Uint8Array)&&(e=f.from(e,e.offset,e.byteLength)),!f.isBuffer(t)||!f.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,r=e.length;for(let i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},f.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return f.alloc(0);let n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;const r=f.allocUnsafe(e);let i=0;for(n=0;n<t.length;++n){let e=t[n];if(X(e,Uint8Array))i+e.length>r.length?(f.isBuffer(e)||(e=f.from(e)),e.copy(r,i)):Uint8Array.prototype.set.call(r,e,i);else{if(!f.isBuffer(e))throw new TypeError('"list" argument must be an Array of Buffers');e.copy(r,i)}i+=e.length}return r},f.byteLength=y,f.prototype._isBuffer=!0,f.prototype.swap16=function(){const t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)w(this,e,e+1);return this},f.prototype.swap32=function(){const t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)w(this,e,e+3),w(this,e+1,e+2);return this},f.prototype.swap64=function(){const t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)w(this,e,e+7),w(this,e+1,e+6),w(this,e+2,e+5),w(this,e+3,e+4);return this},f.prototype.toString=function(){const t=this.length;return 0===t?"":0===arguments.length?O(this,0,t):g.apply(this,arguments)},f.prototype.toLocaleString=f.prototype.toString,f.prototype.equals=function(t){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===f.compare(this,t)},f.prototype.inspect=function(){let t="";const n=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(t+=" ... "),"<Buffer "+t+">"},o&&(f.prototype[o]=f.prototype.inspect),f.prototype.compare=function(t,e,n,r,i){if(X(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),!f.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return-1;if(e>=n)return 1;if(this===t)return 0;let o=(i>>>=0)-(r>>>=0),s=(n>>>=0)-(e>>>=0);const u=Math.min(o,s),a=this.slice(r,i),h=t.slice(e,n);for(let t=0;t<u;++t)if(a[t]!==h[t]){o=a[t],s=h[t];break}return o<s?-1:s<o?1:0},f.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},f.prototype.indexOf=function(t,e,n){return m(this,t,e,n,!0)},f.prototype.lastIndexOf=function(t,e,n){return m(this,t,e,n,!1)},f.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}const i=this.length-e;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let o=!1;for(;;)switch(r){case"hex":return E(this,t,e,n);case"utf8":case"utf-8":return b(this,t,e,n);case"ascii":case"latin1":case"binary":return B(this,t,e,n);case"base64":return A(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return L(this,t,e,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const I=4096;function U(t,e,n){let r="";n=Math.min(t.length,n);for(let i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function S(t,e,n){let r="";n=Math.min(t.length,n);for(let i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function _(t,e,n){const r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);let i="";for(let r=e;r<n;++r)i+=Q[t[r]];return i}function P(t,e,n){const r=t.slice(e,n);let i="";for(let t=0;t<r.length-1;t+=2)i+=String.fromCharCode(r[t]+256*r[t+1]);return i}function C(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function R(t,e,n,r,i,o){if(!f.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function M(t,e,n,r,i){z(e,r,i,t,n,7);let o=Number(e&BigInt(4294967295));t[n++]=o,o>>=8,t[n++]=o,o>>=8,t[n++]=o,o>>=8,t[n++]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[n++]=s,s>>=8,t[n++]=s,s>>=8,t[n++]=s,s>>=8,t[n++]=s,n}function x(t,e,n,r,i){z(e,r,i,t,n,7);let o=Number(e&BigInt(4294967295));t[n+7]=o,o>>=8,t[n+6]=o,o>>=8,t[n+5]=o,o>>=8,t[n+4]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[n+3]=s,s>>=8,t[n+2]=s,s>>=8,t[n+1]=s,s>>=8,t[n]=s,n+8}function N(t,e,n,r,i,o){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function k(t,e,n,r,o){return e=+e,n>>>=0,o||N(t,0,n,4),i.write(t,e,n,r,23,4),n+4}function F(t,e,n,r,o){return e=+e,n>>>=0,o||N(t,0,n,8),i.write(t,e,n,r,52,8),n+8}f.prototype.slice=function(t,e){const n=this.length;(t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);const r=this.subarray(t,e);return Object.setPrototypeOf(r,f.prototype),r},f.prototype.readUintLE=f.prototype.readUIntLE=function(t,e,n){t>>>=0,e>>>=0,n||C(t,e,this.length);let r=this[t],i=1,o=0;for(;++o<e&&(i*=256);)r+=this[t+o]*i;return r},f.prototype.readUintBE=f.prototype.readUIntBE=function(t,e,n){t>>>=0,e>>>=0,n||C(t,e,this.length);let r=this[t+--e],i=1;for(;e>0&&(i*=256);)r+=this[t+--e]*i;return r},f.prototype.readUint8=f.prototype.readUInt8=function(t,e){return t>>>=0,e||C(t,1,this.length),this[t]},f.prototype.readUint16LE=f.prototype.readUInt16LE=function(t,e){return t>>>=0,e||C(t,2,this.length),this[t]|this[t+1]<<8},f.prototype.readUint16BE=f.prototype.readUInt16BE=function(t,e){return t>>>=0,e||C(t,2,this.length),this[t]<<8|this[t+1]},f.prototype.readUint32LE=f.prototype.readUInt32LE=function(t,e){return t>>>=0,e||C(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},f.prototype.readUint32BE=f.prototype.readUInt32BE=function(t,e){return t>>>=0,e||C(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},f.prototype.readBigUInt64LE=Z((function(t){V(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||H(t,this.length-8);const r=e+256*this[++t]+65536*this[++t]+this[++t]*2**24,i=this[++t]+256*this[++t]+65536*this[++t]+n*2**24;return BigInt(r)+(BigInt(i)<<BigInt(32))})),f.prototype.readBigUInt64BE=Z((function(t){V(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||H(t,this.length-8);const r=e*2**24+65536*this[++t]+256*this[++t]+this[++t],i=this[++t]*2**24+65536*this[++t]+256*this[++t]+n;return(BigInt(r)<<BigInt(32))+BigInt(i)})),f.prototype.readIntLE=function(t,e,n){t>>>=0,e>>>=0,n||C(t,e,this.length);let r=this[t],i=1,o=0;for(;++o<e&&(i*=256);)r+=this[t+o]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*e)),r},f.prototype.readIntBE=function(t,e,n){t>>>=0,e>>>=0,n||C(t,e,this.length);let r=e,i=1,o=this[t+--r];for(;r>0&&(i*=256);)o+=this[t+--r]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*e)),o},f.prototype.readInt8=function(t,e){return t>>>=0,e||C(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},f.prototype.readInt16LE=function(t,e){t>>>=0,e||C(t,2,this.length);const n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},f.prototype.readInt16BE=function(t,e){t>>>=0,e||C(t,2,this.length);const n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},f.prototype.readInt32LE=function(t,e){return t>>>=0,e||C(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},f.prototype.readInt32BE=function(t,e){return t>>>=0,e||C(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},f.prototype.readBigInt64LE=Z((function(t){V(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||H(t,this.length-8);const r=this[t+4]+256*this[t+5]+65536*this[t+6]+(n<<24);return(BigInt(r)<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+this[++t]*2**24)})),f.prototype.readBigInt64BE=Z((function(t){V(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||H(t,this.length-8);const r=(e<<24)+65536*this[++t]+256*this[++t]+this[++t];return(BigInt(r)<<BigInt(32))+BigInt(this[++t]*2**24+65536*this[++t]+256*this[++t]+n)})),f.prototype.readFloatLE=function(t,e){return t>>>=0,e||C(t,4,this.length),i.read(this,t,!0,23,4)},f.prototype.readFloatBE=function(t,e){return t>>>=0,e||C(t,4,this.length),i.read(this,t,!1,23,4)},f.prototype.readDoubleLE=function(t,e){return t>>>=0,e||C(t,8,this.length),i.read(this,t,!0,52,8)},f.prototype.readDoubleBE=function(t,e){return t>>>=0,e||C(t,8,this.length),i.read(this,t,!1,52,8)},f.prototype.writeUintLE=f.prototype.writeUIntLE=function(t,e,n,r){t=+t,e>>>=0,n>>>=0,r||R(this,t,e,n,Math.pow(2,8*n)-1,0);let i=1,o=0;for(this[e]=255&t;++o<n&&(i*=256);)this[e+o]=t/i&255;return e+n},f.prototype.writeUintBE=f.prototype.writeUIntBE=function(t,e,n,r){t=+t,e>>>=0,n>>>=0,r||R(this,t,e,n,Math.pow(2,8*n)-1,0);let i=n-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+n},f.prototype.writeUint8=f.prototype.writeUInt8=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,1,255,0),this[e]=255&t,e+1},f.prototype.writeUint16LE=f.prototype.writeUInt16LE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},f.prototype.writeUint16BE=f.prototype.writeUInt16BE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},f.prototype.writeUint32LE=f.prototype.writeUInt32LE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},f.prototype.writeUint32BE=f.prototype.writeUInt32BE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},f.prototype.writeBigUInt64LE=Z((function(t,e=0){return M(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),f.prototype.writeBigUInt64BE=Z((function(t,e=0){return x(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),f.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e>>>=0,!r){const r=Math.pow(2,8*n-1);R(this,t,e,n,r-1,-r)}let i=0,o=1,s=0;for(this[e]=255&t;++i<n&&(o*=256);)t<0&&0===s&&0!==this[e+i-1]&&(s=1),this[e+i]=(t/o|0)-s&255;return e+n},f.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e>>>=0,!r){const r=Math.pow(2,8*n-1);R(this,t,e,n,r-1,-r)}let i=n-1,o=1,s=0;for(this[e+i]=255&t;--i>=0&&(o*=256);)t<0&&0===s&&0!==this[e+i+1]&&(s=1),this[e+i]=(t/o|0)-s&255;return e+n},f.prototype.writeInt8=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},f.prototype.writeInt16LE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},f.prototype.writeInt16BE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},f.prototype.writeInt32LE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},f.prototype.writeInt32BE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},f.prototype.writeBigInt64LE=Z((function(t,e=0){return M(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),f.prototype.writeBigInt64BE=Z((function(t,e=0){return x(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),f.prototype.writeFloatLE=function(t,e,n){return k(this,t,e,!0,n)},f.prototype.writeFloatBE=function(t,e,n){return k(this,t,e,!1,n)},f.prototype.writeDoubleLE=function(t,e,n){return F(this,t,e,!0,n)},f.prototype.writeDoubleBE=function(t,e,n){return F(this,t,e,!1,n)},f.prototype.copy=function(t,e,n,r){if(!f.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);const i=r-n;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,n,r):Uint8Array.prototype.set.call(r,this.subarray(n,r),e),i},f.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!f.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(1===t.length){const e=t.charCodeAt(0);("utf8"===r&&e<128||"latin1"===r)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;let i;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(i=e;i<n;++i)this[i]=t;else{const o=f.isBuffer(t)?t:f.from(t,r),s=o.length;if(0===s)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<n-e;++i)this[i+e]=o[i%s]}return this};const j={};function $(t,e,n){j[t]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function D(t){let e="",n=t.length;const r="-"===t[0]?1:0;for(;n>=r+4;n-=3)e=`_${t.slice(n-3,n)}${e}`;return`${t.slice(0,n)}${e}`}function z(t,e,n,r,i,o){if(t>n||t<e){const r="bigint"==typeof e?"n":"";let i;throw i=o>3?0===e||e===BigInt(0)?`>= 0${r} and < 2${r} ** ${8*(o+1)}${r}`:`>= -(2${r} ** ${8*(o+1)-1}${r}) and < 2 ** ${8*(o+1)-1}${r}`:`>= ${e}${r} and <= ${n}${r}`,new j.ERR_OUT_OF_RANGE("value",i,t)}!function(t,e,n){V(e,"offset"),void 0!==t[e]&&void 0!==t[e+n]||H(e,t.length-(n+1))}(r,i,o)}function V(t,e){if("number"!=typeof t)throw new j.ERR_INVALID_ARG_TYPE(e,"number",t)}function H(t,e,n){if(Math.floor(t)!==t)throw V(t,n),new j.ERR_OUT_OF_RANGE(n||"offset","an integer",t);if(e<0)throw new j.ERR_BUFFER_OUT_OF_BOUNDS;throw new j.ERR_OUT_OF_RANGE(n||"offset",`>= ${n?1:0} and <= ${e}`,t)}$("ERR_BUFFER_OUT_OF_BOUNDS",(function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),$("ERR_INVALID_ARG_TYPE",(function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`}),TypeError),$("ERR_OUT_OF_RANGE",(function(t,e,n){let r=`The value of "${t}" is out of range.`,i=n;return Number.isInteger(n)&&Math.abs(n)>2**32?i=D(String(n)):"bigint"==typeof n&&(i=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(i=D(i)),i+="n"),r+=` It must be ${e}. Received ${i}`,r}),RangeError);const W=/[^+/0-9A-Za-z-_]/g;function K(t,e){let n;e=e||1/0;const r=t.length;let i=null;const o=[];for(let s=0;s<r;++s){if(n=t.charCodeAt(s),n>55295&&n<57344){if(!i){if(n>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===r){(e-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;o.push(n)}else if(n<2048){if((e-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function G(t){return r.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(W,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function q(t,e,n,r){let i;for(i=0;i<r&&!(i+n>=e.length||i>=t.length);++i)e[i+n]=t[i];return i}function X(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function Y(t){return t!=t}const Q=function(){const t="0123456789abcdef",e=new Array(256);for(let n=0;n<16;++n){const r=16*n;for(let i=0;i<16;++i)e[r+i]=t[n]+t[i]}return e}();function Z(t){return"undefined"==typeof BigInt?J:t}function J(){throw new Error("BigInt not supported")}},352:(t,e)=>{"use strict";var n,r,i,o;Object.defineProperty(e,"__esModule",{value:!0}),e.VOLUME=e.RATE=e.PITCH=e.OUTPUT_FORMAT=void 0,function(t){t.AUDIO_24KHZ_48KBITRATE_MONO_MP3="audio-24khz-48kbitrate-mono-mp3",t.AUDIO_24KHZ_96KBITRATE_MONO_MP3="audio-24khz-96kbitrate-mono-mp3",t.WEBM_24KHZ_16BIT_MONO_OPUS="webm-24khz-16bit-mono-opus"}(n||(e.OUTPUT_FORMAT=n={})),function(t){t.X_LOW="x-low",t.LOW="low",t.MEDIUM="medium",t.HIGH="high",t.X_HIGH="x-high",t.DEFAULT="default"}(r||(e.PITCH=r={})),function(t){t.X_SLOW="x-slow",t.SLOW="slow",t.MEDIUM="medium",t.FAST="fast",t.X_FAST="x-fast",t.DEFAULT="default"}(i||(e.RATE=i={})),function(t){t.SILENT="silent",t.X_SOFT="x-soft",t.SOFT="soft",t.MEDIUM="medium",t.LOUD="loud",t.X_LOUD="x-LOUD",t.DEFAULT="default"}(o||(e.VOLUME=o={}))},361:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.VOLUME=e.RATE=e.PITCH=e.OUTPUT_FORMAT=e.ProsodyOptions=e.EdgeTTSClient=void 0;var r=n(778);Object.defineProperty(e,"EdgeTTSClient",{enumerable:!0,get:function(){return r.EdgeTTSClient}});var i=n(778);Object.defineProperty(e,"ProsodyOptions",{enumerable:!0,get:function(){return i.ProsodyOptions}});var o=n(352);Object.defineProperty(e,"OUTPUT_FORMAT",{enumerable:!0,get:function(){return o.OUTPUT_FORMAT}}),Object.defineProperty(e,"PITCH",{enumerable:!0,get:function(){return o.PITCH}}),Object.defineProperty(e,"RATE",{enumerable:!0,get:function(){return o.RATE}}),Object.defineProperty(e,"VOLUME",{enumerable:!0,get:function(){return o.VOLUME}})},526:(t,e)=>{"use strict";e.byteLength=function(t){var e=u(t),n=e[0],r=e[1];return 3*(n+r)/4-r},e.toByteArray=function(t){var e,n,o=u(t),s=o[0],f=o[1],a=new i(function(t,e,n){return 3*(e+n)/4-n}(0,s,f)),h=0,c=f>0?s-4:s;for(n=0;n<c;n+=4)e=r[t.charCodeAt(n)]<<18|r[t.charCodeAt(n+1)]<<12|r[t.charCodeAt(n+2)]<<6|r[t.charCodeAt(n+3)],a[h++]=e>>16&255,a[h++]=e>>8&255,a[h++]=255&e;return 2===f&&(e=r[t.charCodeAt(n)]<<2|r[t.charCodeAt(n+1)]>>4,a[h++]=255&e),1===f&&(e=r[t.charCodeAt(n)]<<10|r[t.charCodeAt(n+1)]<<4|r[t.charCodeAt(n+2)]>>2,a[h++]=e>>8&255,a[h++]=255&e),a},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,o=[],s=16383,u=0,a=r-i;u<a;u+=s)o.push(f(t,u,u+s>a?a:u+s));return 1===i?(e=t[r-1],o.push(n[e>>2]+n[e<<4&63]+"==")):2===i&&(e=(t[r-2]<<8)+t[r-1],o.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"=")),o.join("")};for(var n=[],r=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0;s<64;++s)n[s]=o[s],r[o.charCodeAt(s)]=s;function u(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function f(t,e,r){for(var i,o,s=[],u=e;u<r;u+=3)i=(t[u]<<16&16711680)+(t[u+1]<<8&65280)+(255&t[u+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join("")}r["-".charCodeAt(0)]=62,r["_".charCodeAt(0)]=63},778:function(t,e,n){"use strict";var r=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function s(t){try{f(r.next(t))}catch(t){o(t)}}function u(t){try{f(r.throw(t))}catch(t){o(t)}}function f(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,u)}f((r=r.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.EdgeTTSClient=e.ProsodyOptions=void 0;const i=n(287),o=n(352);void 0===globalThis.Buffer&&(globalThis.Buffer=i.Buffer);class s{constructor(){this.eventListeners={data:[],close:[],end:[]}}on(t,e){this.eventListeners[t].push(e)}emit(t,e){this.eventListeners[t].forEach((t=>t(e)))}}class u{constructor(){this.pitch="+0Hz",this.rate=1,this.volume=100}}e.ProsodyOptions=u;class f{constructor(t=!1){this.ws=null,this.voice=null,this.voiceLocale=null,this.outputFormat=null,this.requestQueue={},this.connectionStartTime=0,this.enableLogging=t,this.isBrowser="undefined"!=typeof window&&void 0!==window.document}log(...t){this.enableLogging&&console.log(...t)}sendMessage(t){return r(this,void 0,void 0,(function*(){var e,n;for(let t=1;t<=3&&(null===(e=this.ws)||void 0===e?void 0:e.readyState)!==WebSocket.OPEN;t++)1===t&&(this.connectionStartTime=Date.now()),this.log(`Connecting... attempt ${t}`),yield this.initWebSocket();null===(n=this.ws)||void 0===n||n.send(t)}))}initWebSocket(){this.ws=new WebSocket(f.SYNTH_URL),this.ws.binaryType="arraybuffer";let t=[];return new Promise(((e,n)=>{this.ws.onopen=()=>{this.log("Connected in",(Date.now()-this.connectionStartTime)/1e3,"seconds"),this.sendMessage(this.getConfigMessage()).then(e)},this.ws.onmessage=e=>this.handleMessage(e,t),this.ws.onclose=()=>this.handleClose(),this.ws.onerror=t=>n(`Connection Error: ${t}`)}))}handleMessage(t,e){var n;const r=i.Buffer.from(t.data),o=r.toString(),s=/X-RequestId:(.*?)\r\n/.exec(o),u=s?s[1]:"";if(o.includes("Path:turn.start"))e.length=0;else if(o.includes("Path:turn.end"))null===(n=this.requestQueue[u])||void 0===n||n.emit("end",e);else if(o.includes("Path:audio"))this.cacheAudioData(r,u);else if(o.includes("Path:audio.metadata")){const t=o.indexOf("{");e.push(JSON.parse(o.slice(t)).Metadata[0])}else this.log("Unknown Message",o)}handleClose(){this.log("Disconnected after:",(Date.now()-this.connectionStartTime)/1e3,"seconds");for(const t in this.requestQueue)this.requestQueue[t].emit("close",null)}cacheAudioData(t,e){var n;const r=(new TextEncoder).encode(f.BINARY_DELIM),i=this.findDelimiterIndex(t,r);if(-1===i)return void this.log("Delimiter not found in the buffer.");const o=i+r.length,s=t.slice(o);null===(n=this.requestQueue[e])||void 0===n||n.emit("data",s),this.log("Received audio chunk of size:",null==s?void 0:s.length)}findDelimiterIndex(t,e){for(let n=0;n<=t.length-e.length;n++){let r=!0;for(let i=0;i<e.length;i++)if(t[n+i]!==e[i]){r=!1;break}if(r)return n}return-1}getConfigMessage(){return`Content-Type:application/json; charset=utf-8\r\nPath:speech.config\r\n\r\n{\n            "context": {\n                "synthesis": {\n                    "audio": {\n                        "metadataoptions": {\n                            "sentenceBoundaryEnabled": "true",\n                            "wordBoundaryEnabled": "true"\n                        },\n                        "outputFormat": "${this.outputFormat}"\n                    }\n                }\n            }\n        }`}getVoices(){return fetch(f.VOICES_URL).then((t=>t.json())).catch((t=>Promise.reject(t)))}setMetadata(t,e,n){return r(this,void 0,void 0,(function*(){if(this.voice=t,this.outputFormat=e,this.voiceLocale=n||this.inferLocaleFromVoiceName(t),!this.voiceLocale)throw new Error("Could not infer voiceLocale from voiceName!");this.ws&&this.ws.readyState===WebSocket.OPEN||(this.connectionStartTime=Date.now(),yield this.initWebSocket())}))}inferLocaleFromVoiceName(t){const e=f.VOICE_LANG_REGEX.exec(t);return e?e[0]:null}close(){var t;null===(t=this.ws)||void 0===t||t.close()}toStream(t,e=new u){return this.sendSSMLRequest(this.buildSSML(t,e))}buildSSML(t,e){return`<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xmlns:mstts="https://www.w3.org/2001/mstts" xml:lang="${this.voiceLocale}">\n            <voice name="${this.voice}">\n                <prosody pitch="${e.pitch}" rate="${e.rate}" volume="${e.volume}">\n                    ${t}\n                </prosody>\n            </voice>\n        </speak>`}sendSSMLRequest(t){if(!this.ws)throw new Error("WebSocket not initialized. Call setMetadata first.");const e=function(){const t=new Uint8Array(16);return window.crypto.getRandomValues(t),Array.from(t,(t=>`0${t.toString(16)}`.slice(-2))).join("")}(),n=`X-RequestId:${e}\r\nContent-Type:application/ssml+xml\r\nPath:ssml\r\n\r\n${t.trim()}`,r=new s;return this.requestQueue[e]=r,this.sendMessage(n).then(),r}}e.EdgeTTSClient=f,f.OUTPUT_FORMAT=o.OUTPUT_FORMAT,f.CLIENT_TOKEN="6A5AA1D4EAFF4E9FB37E23D68491D6F4",f.VOICES_URL=`https://speech.platform.bing.com/consumer/speech/synthesize/readaloud/voices/list?trustedclienttoken=${f.CLIENT_TOKEN}`,f.SYNTH_URL=`wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?TrustedClientToken=${f.CLIENT_TOKEN}`,f.BINARY_DELIM="Path:audio\r\n",f.VOICE_LANG_REGEX=/\w{2}-\w{2}/}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r].call(o.exports,o,o.exports,n),o.exports}const{EdgeTTSClient:r,ProsodyOptions:i,OUTPUT_FORMAT:o}=n(361),{Buffer:s}=n(287),{EventEmitter:u}=n(7);window.Buffer=s;class f{constructor(){this.audioContext=null,this.currentSource=null,this.isPlaying=!1,this.ttsClient=new r(!1),this.defaultVoice="en-CA-LiamNeural",this.textArea=null,this.voiceSelect=null,this.rateSelect=null,this.mediaSource=null,this.sourceBuffer=null,this.currentAudio=null,this.audioQueue=[],this.isPaused=!1,this.pendingPlay=null,this.activeStream=null,this.audioChunks=[],this.currentChunkIndex=0,this.isInitialized=!1,this.textArea=document.getElementById("text"),this.voiceSelect=document.getElementById("voice"),this.rateSelect=document.getElementById("rate"),this.speakButton=document.getElementById("speak"),this.pauseButton=document.getElementById("pause"),this.resumeButton=document.getElementById("resume"),this.stopButton=document.getElementById("stop"),this.speakButton&&this.speakButton.addEventListener("click",(()=>this.speak())),this.pauseButton&&this.pauseButton.addEventListener("click",(()=>this.pause())),this.resumeButton&&this.resumeButton.addEventListener("click",(()=>this.resume())),this.stopButton&&this.stopButton.addEventListener("click",(()=>this.stop()));const t=()=>{this.audioContext||(this.audioContext=new(window.AudioContext||window.webkitAudioContext))};document.addEventListener("click",t,{once:!0}),this.ensureAudioContext=t,this.initializeTTS(),this.voiceSelect&&this.loadVoices()}async loadVoices(){try{const t=await this.ttsClient.getVoices();this.voiceSelect.innerHTML="";const e=t.filter((t=>t.Locale.startsWith("en-")));e.sort(((t,e)=>t.Locale!==e.Locale?t.Locale.localeCompare(e.Locale):t.FriendlyName.localeCompare(e.FriendlyName)));const n=e.reduce(((t,e)=>{const n=e.Locale;return t[n]||(t[n]=[]),t[n].push(e),t}),{}),r={"en-US":"English (United States)","en-GB":"English (United Kingdom)","en-AU":"English (Australia)","en-CA":"English (Canada)","en-IN":"English (India)","en-IE":"English (Ireland)","en-NZ":"English (New Zealand)","en-ZA":"English (South Africa)"};for(const[t,e]of Object.entries(n)){const n=document.createElement("optgroup");n.label=r[t]||t;for(const t of e){const e=document.createElement("option");e.value=t.ShortName,e.textContent=`${t.FriendlyName} (${t.Gender})`,n.appendChild(e)}this.voiceSelect.appendChild(n)}const i=e.find((t=>"en-US-JennyNeural"===t.ShortName))||e[0];i&&(this.voiceSelect.value=i.ShortName)}catch(t){console.error("Failed to load voices:",t)}}getLanguageName(t){try{return new Intl.DisplayNames(["en"],{type:"language"}).of(t.split("-")[0])}catch(e){return t}}async initializeTTS(){try{this.isInitialized||(await this.ttsClient.setMetadata("en-US-JennyNeural",o.AUDIO_24KHZ_48KBITRATE_MONO_MP3),this.isInitialized=!0)}catch(t){console.error("Failed to initialize TTS:",t),this.isInitialized=!1}}splitIntoSentences(t){return t.match(/[^.!?]+[.!?]+/g)||[t]}async processChunk(t,e,n){return new Promise(((e,r)=>{if(!t||0===t.trim().length)return void e(null);const i=this.ttsClient.toStream(t.trim(),n),o=[];let u=!1;i.on("data",(t=>{t&&0!==t.length&&(u=!0,"fff3"===t.slice(0,2).toString("hex")&&o.push(t))})),i.on("end",(()=>{if(!u||0===o.length)return void e(null);const t=o.reduce(((t,e)=>t+e.length),0),n=s.concat(o,t);e(n)})),i.on("close",(()=>{u||e(null)}))}))}async speak(t,e,n){this.audioContext||this.ensureAudioContext();const r=t||(this.textArea?this.textArea.value:""),s=e||(this.voiceSelect?this.voiceSelect.value:this.defaultVoice),u=n||(this.rateSelect?this.rateSelect.value:"1.0");if(r){this.isPlaying&&this.stop();try{this.isPlaying=!0,this.isInitialized||await this.initializeTTS(),await this.ttsClient.setMetadata(s,o.AUDIO_24KHZ_48KBITRATE_MONO_MP3);const t=this.splitIntoSentences(r),e=[];for(let n=0;n<t.length;n+=3)e.push(t.slice(n,n+3).join(" "));const n=new i;n.rate=u;for(const t of e){if(!this.isPlaying)break;try{const e=await this.processChunk(t,s,n);e&&this.isPlaying&&await this.playAudioBuffer(e)}catch(t){throw console.error("Error during speech synthesis:",t),t}}}catch(t){throw console.error("Error during speech synthesis:",t),this.handleSpeakCompletion(!1),t}this.isPlaying&&this.handleSpeakCompletion(!0)}}handleSpeakCompletion(t){this.isPlaying=!1,this.isPaused=!1,this.currentAudio=null,this.pendingPlay=null,this.audioQueue=[];const e=new CustomEvent("tts-complete",{detail:{success:t}});window.dispatchEvent(e),this.speakButton&&(this.speakButton.disabled=!1)}async playAudioBuffer(t){if(this.isPlaying)try{const e=new Audio,n=new MediaSource;await new Promise(((r,i)=>{let o=null;if(n.addEventListener("sourceopen",(()=>{try{o=n.addSourceBuffer("audio/mpeg"),o.addEventListener("updateend",(()=>{"open"===n.readyState&&(n.endOfStream(),this.isPlaying&&!this.isPaused&&(this.pendingPlay=e.play(),this.pendingPlay.catch((t=>{this.isPlaying&&i(t)}))))})),e.addEventListener("error",(()=>{this.isPlaying&&i(new Error("Audio playback error: "+(e.error?e.error.message:"unknown error")))})),e.addEventListener("ended",(()=>{this.currentAudio===e&&(this.currentAudio=null),r()})),o.appendBuffer(t)}catch(t){this.isPlaying&&i(t)}}),{once:!0}),e.src=URL.createObjectURL(n),this.currentAudio)try{this.currentAudio.pause(),URL.revokeObjectURL(this.currentAudio.src),this.currentAudio.src="",this.currentAudio.remove()}catch(t){console.warn("Error cleaning up old audio:",t)}this.currentAudio=e}))}catch(t){if(this.isPlaying)throw console.error("Error playing audio buffer:",t),t}}async stop(){if(this.isPlaying=!1,this.isPaused=!1,this.pendingPlay){try{await this.pendingPlay}catch(t){}this.pendingPlay=null}if(this.currentAudio)try{this.currentAudio.pause(),URL.revokeObjectURL(this.currentAudio.src),this.currentAudio.src="",this.currentAudio.remove(),this.currentAudio=null}catch(t){console.warn("Error cleaning up audio:",t)}if(this.audioQueue=[],this.mediaSource&&"open"===this.mediaSource.readyState)try{this.mediaSource.endOfStream()}catch(t){}}async pause(){if(this.currentAudio&&this.isPlaying){if(this.isPaused=!0,this.pendingPlay){try{await this.pendingPlay}catch(t){console.warn("Error waiting for pending play:",t)}this.pendingPlay=null}try{await this.currentAudio.pause()}catch(t){console.error("Error pausing audio:",t)}}}async resume(){if(this.currentAudio&&this.isPlaying&&this.isPaused){this.isPaused=!1;try{this.pendingPlay=this.currentAudio.play(),await this.pendingPlay}catch(t){console.error("Error resuming audio:",t),this.pendingPlay=null}}}}let a;
document.addEventListener("DOMContentLoaded", () => {
    a = new f;
    window.edgeTTSHandler = a;
});
window.ttsSpeak = async (t, e = "en-CA-LiamNeural", n = "1.0") => {
    a || (a = new f);
    window.edgeTTSHandler = a;
    await a.speak(t, e, n)
};
window.ttsPause = () => a.pause();
window.ttsResume = () => a.resume();
window.ttsStop = () => a.stop();
})();
