<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimized TTS Test - Two-Stage Strategy</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tabler-icons/3.34.0/tabler-icons.min.css">
    <style>
        body {
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }
        .action-button {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 6px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .action-button:hover {
            background: #e9ecef;
            color: #495057;
        }
        .tts-button-group {
            display: flex;
            gap: 4px;
        }
        .stop-button.hidden {
            display: none;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .performance-metrics {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .metric {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            background: white;
            border-radius: 3px;
            font-weight: bold;
        }
        .strategy-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 10px;
        }
        .two-stage {
            background: #4caf50;
            color: white;
        }
        .single-stage {
            background: #ff9800;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Optimized TTS Test - Two-Stage Strategy</h1>
        <p>This page tests the new optimized TTS implementation with two-stage strategy for ultra-low latency.</p>
        
        <div class="alert alert-info">
            <strong>How it works:</strong>
            <ul>
                <li><strong>Short texts (≤5 words):</strong> Single-stage playback</li>
                <li><strong>Long texts (>5 words):</strong> Two-stage strategy - first 2 words play immediately while remaining text loads in parallel</li>
                <li><strong>Warm-up:</strong> TTS service is pre-warmed on page load to reduce first-use latency</li>
            </ul>
        </div>
        
        <!-- Performance Metrics -->
        <div class="performance-metrics">
            <h5>📊 Performance Metrics</h5>
            <div id="metrics">
                <span class="metric">First Audio Start: <span id="firstAudioTime">-</span>ms</span>
                <span class="metric">Total Duration: <span id="totalDuration">-</span>ms</span>
                <span class="metric">Strategy: <span id="strategy">-</span></span>
            </div>
        </div>
        
        <!-- Test Messages -->
        <div class="message assistant">
            <p>Hello world!</p>
            <span class="strategy-badge single-stage">SINGLE-STAGE</span>
            <div class="message-actions">
                <div class="tts-button-group">
                    <button class="action-button" title="Listen">
                        <i class="ti ti-player-play"></i>
                    </button>
                    <button class="action-button stop-button hidden" title="Stop">
                        <i class="ti ti-player-stop"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="message assistant">
            <p>This is a longer message that should trigger the two-stage TTS strategy for optimal latency and user experience.</p>
            <span class="strategy-badge two-stage">TWO-STAGE</span>
            <div class="message-actions">
                <div class="tts-button-group">
                    <button class="action-button" title="Listen">
                        <i class="ti ti-player-play"></i>
                    </button>
                    <button class="action-button stop-button hidden" title="Stop">
                        <i class="ti ti-player-stop"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="message assistant">
            <p>Here's an even longer message to demonstrate the effectiveness of the two-stage TTS strategy. The first two words should start playing almost immediately while the rest of the text is being processed in parallel. This approach significantly reduces the perceived latency for long AI responses, making the conversation feel more natural and responsive. The seamless concatenation ensures there are no gaps or interruptions in the audio playback.</p>
            <span class="strategy-badge two-stage">TWO-STAGE</span>
            <div class="message-actions">
                <div class="tts-button-group">
                    <button class="action-button" title="Listen">
                        <i class="ti ti-player-play"></i>
                    </button>
                    <button class="action-button stop-button hidden" title="Stop">
                        <i class="ti ti-player-stop"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Custom Test -->
        <div class="mt-4">
            <h3>🧪 Custom Test</h3>
            <div class="row">
                <div class="col-md-8">
                    <textarea id="customText" class="form-control" rows="4" placeholder="Enter your own text to test the optimized TTS...">Welcome to the future of Text-to-Speech technology! This optimized implementation uses a sophisticated two-stage strategy to minimize latency and provide the best possible user experience. Try it out and notice how quickly the audio starts playing!</textarea>
                </div>
                <div class="col-md-4">
                    <button id="testCustomTTS" class="btn btn-primary w-100">Test Custom TTS</button>
                    <button id="stopCustomTTS" class="btn btn-danger w-100 mt-2">Stop TTS</button>
                    <div class="mt-3">
                        <small class="text-muted">Word count: <span id="wordCount">0</span></small><br>
                        <small class="text-muted">Strategy: <span id="predictedStrategy">Single-stage</span></small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Log -->
        <div class="mt-4">
            <h3>🔍 Debug Log</h3>
            <div id="debugLog" class="log">Optimized TTS Test initialized...\n</div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Load edgeTTS -->
    <script src="/src/js/create-new/edgeTTS.js"></script>
    
    <!-- Load optimized TTS Integration -->
    <script src="/src/js/create-new/ai-chat-integration.js"></script>
    
    <script>
        // Performance tracking
        let performanceMetrics = {
            startTime: 0,
            firstAudioTime: 0,
            totalDuration: 0,
            strategy: ''
        };
        
        // Debug logging
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        // Update performance metrics display
        function updateMetrics() {
            document.getElementById('firstAudioTime').textContent = performanceMetrics.firstAudioTime || '-';
            document.getElementById('totalDuration').textContent = performanceMetrics.totalDuration || '-';
            document.getElementById('strategy').textContent = performanceMetrics.strategy || '-';
        }
        
        // Update word count and predicted strategy
        function updateWordCount() {
            const text = document.getElementById('customText').value;
            const words = text.trim().split(/\s+/).filter(w => w.length > 0);
            const wordCount = words.length;
            const strategy = wordCount > 5 ? 'Two-stage' : 'Single-stage';
            
            document.getElementById('wordCount').textContent = wordCount;
            document.getElementById('predictedStrategy').textContent = strategy;
        }
        
        // Custom TTS test
        document.getElementById('testCustomTTS').addEventListener('click', async () => {
            const text = document.getElementById('customText').value;
            const words = text.trim().split(/\s+/).filter(w => w.length > 0);
            
            log(`🧪 Testing custom TTS: ${words.length} words`);
            log(`Text: "${text.substring(0, 100)}..."`);
            
            performanceMetrics.startTime = performance.now();
            performanceMetrics.strategy = words.length > 5 ? 'Two-stage' : 'Single-stage';
            
            try {
                // Use the optimized TTS function if available
                if (window.performOptimizedTTS) {
                    await window.performOptimizedTTS(text, 'en-CA-LiamNeural', '1.0');
                } else {
                    log('⚠️ Optimized TTS not available, using fallback');
                    if (window.ttsSpeak) {
                        await window.ttsSpeak(text, 'en-CA-LiamNeural', '1.0');
                    } else {
                        throw new Error('No TTS available');
                    }
                }
                
                performanceMetrics.totalDuration = Math.round(performance.now() - performanceMetrics.startTime);
                updateMetrics();
                log(`✅ Custom TTS completed in ${performanceMetrics.totalDuration}ms`);
                
            } catch (error) {
                log(`❌ Custom TTS failed: ${error.message}`);
            }
        });
        
        // Stop TTS
        document.getElementById('stopCustomTTS').addEventListener('click', () => {
            log('🛑 Stopping custom TTS');
            if (window.ttsStop) window.ttsStop();
            if (window.edgeTTSHandler && window.edgeTTSHandler.stop) window.edgeTTSHandler.stop();
        });
        
        // Update word count on text change
        document.getElementById('customText').addEventListener('input', updateWordCount);
        
        // Initialize
        setTimeout(() => {
            log('Checking optimized TTS availability...');
            log(`edgeTTSHandler: ${!!window.edgeTTSHandler} (${typeof window.edgeTTSHandler})`);
            log(`ttsSpeak: ${!!window.ttsSpeak} (${typeof window.ttsSpeak})`);
            log(`performOptimizedTTS: ${!!window.performOptimizedTTS} (${typeof window.performOptimizedTTS})`);
            
            updateWordCount();
            
            if (window.edgeTTSHandler) {
                log(`edgeTTSHandler methods: ${Object.getOwnPropertyNames(window.edgeTTSHandler).join(', ')}`);
            }
            
            log('🔥 TTS warm-up should be starting...');
        }, 1000);
        
        log('Optimized TTS Test loaded');
    </script>
</body>
</html>
